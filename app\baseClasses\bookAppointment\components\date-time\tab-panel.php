<div class="p-6">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Calendar Section -->
    <div class="border rounded-lg p-2 md:p-4">
      <div class="flex items-center justify-between mb-4">
        <button type="button" id="prev-month" class="p-1 hover:bg-gray-100 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-chevron-left w-5 h-5 text-gray-600">
            <path d="m15 18-6-6 6-6"></path>
          </svg>
        </button>
        <h3 id="calendar-title" class="text-lg font-medium text-gray-900"></h3>
        <button type="button" id="next-month" class="p-1 hover:bg-gray-100 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-chevron-right w-5 h-5 text-gray-600">
            <path d="m9 18 6-6-6-6"></path>
          </svg>
        </button>
      </div>

      <!-- Calendar Grid -->
      <div class="grid grid-cols-7 gap-2 mb-2">
        <div class="text-center text-sm text-gray-500 font-medium">Sun</div>
        <div class="text-center text-sm text-gray-500 font-medium">Mon</div>
        <div class="text-center text-sm text-gray-500 font-medium">Tue</div>
        <div class="text-center text-sm text-gray-500 font-medium">Wed</div>
        <div class="text-center text-sm text-gray-500 font-medium">Thu</div>
        <div class="text-center text-sm text-gray-500 font-medium">Fri</div>
        <div class="text-center text-sm text-gray-500 font-medium">Sat</div>
      </div>

      <!-- Calendar Days -->
      <div id="calendar-days" class="grid grid-cols-7 gap-2">
        <!-- Days will be dynamically inserted here -->
      </div>
    </div>

    <!-- Time Slot Section -->
    <div class="border rounded-lg p-2 md:p-4">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Available Time Slots</h3>
      <div class="relative h-52">
        <!-- For text-only content (like "Please select a date") -->
        <div id="timeSlotLists" class="h-full flex items-center justify-center">
          <p class="loader-class text-gray-500 font-semibold">Please select a date</p>
        </div>

        <!-- For time slot data (initially hidden, show when data is available) -->
        <div id="timeSlotGrid" class="h-full grid grid-cols-3 gap-2 overflow-y-auto hidden">
          <!-- Time slots will be dynamically added here -->
        </div>
      </div>
      <!-- <div class="grid grid-cols-3 gap-2 h-52 overflow-y-auto" id="timeSlotLists">
                <p class="loader-class text-center text-gray-500 font-semibold">Please select a date</p>
            </div> -->
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Attach a click event listener to the entire document
    document.addEventListener("click", function (event) {
      // Check if the clicked element is a button inside a date or time container
      const button = event.target.closest("button");
      if (button && button.closest(".date-container")) {
        // Reset styles for all buttons in date and time containers
        document.querySelectorAll(".date-container").forEach(function (btn) {
          btn.classList.remove("bg-violet-600", "text-white");
          btn.classList.add("hover:bg-violet-50", "text-gray-700");
        });

        // Apply active styles to the clicked button
        button.classList.remove("hover:bg-violet-50", "text-gray-700");
        button.classList.add("bg-violet-600", "text-white");
      }
    });
  });

  document.addEventListener("DOMContentLoaded", function () {
    // Attach a click event listener to the entire document
    document.addEventListener("click", function (event) {
      const timeSlotDiv = event.target.closest(".time-container");
      if (timeSlotDiv) {
        // Reset styles for all time slots
        document.querySelectorAll(".time-container").forEach(function (btn) {
          btn.classList.remove("bg-violet-600", "text-white");
          btn.classList.add("bg-gray-50", "hover:bg-violet-50", "text-gray-700");
        });

        // Apply active styles to the clicked time slot
        timeSlotDiv.classList.remove("bg-gray-50", "hover:bg-violet-50", "text-gray-700");
        timeSlotDiv.classList.add("bg-violet-600", "text-white");

        // Find the associated radio input and check it
        const radioInput = timeSlotDiv.querySelector("input[type='radio']");
        if (radioInput) {
          radioInput.checked = true; // Mark the radio button as checked
        }
      }
    });
  });
</script>