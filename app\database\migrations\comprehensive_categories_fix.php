<?php
/**
 * Comprehensive Categories System Fix Migration
 * This migration fixes all category-related issues in production
 * Date: 2025-06-30
 */

if (!defined('ABSPATH')) {
    exit;
}

class ComprehensiveCategoriesFix {
    
    /**
     * Run the migration
     */
    public function up() {
        global $wpdb;
        
        try {
            error_log("Starting ComprehensiveCategoriesFix migration");
            
            // 1. Ensure categories table exists with proper structure
            $this->ensureCategoriesTable();
            
            // 2. Ensure services table has required columns
            $this->ensureServicesTableStructure();
            
            // 3. Fix orphaned category references (set to NULL)
            $this->fixOrphanedCategoryReferences();
            
            // 4. Map services to existing diagnostics category if available
            $this->mapToExistingCategories();
            
            // 5. Clean up invalid data
            $this->cleanupInvalidData();
            
            // 6. Fix missing timestamps
            $this->fixMissingTimestamps();
            
            error_log("ComprehensiveCategoriesFix migration completed successfully");
            return true;
            
        } catch (Exception $e) {
            error_log("ComprehensiveCategoriesFix migration failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Ensure categories table exists with proper structure
     */
    private function ensureCategoriesTable() {
        global $wpdb;
        
        $categories_table = $wpdb->prefix . 'kc_categories';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table;
        
        if ($table_exists) {
            error_log("Categories table already exists");
        } else {
            error_log("Categories table does not exist - please run the main categories migration first");
        }
    }
    
    /**
     * Ensure services table has required columns
     */
    private function ensureServicesTableStructure() {
        global $wpdb;
        
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Check if services table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$services_table'") != $services_table) {
            throw new Exception("Services table does not exist!");
        }
        
        // Get current columns
        $columns = $wpdb->get_results("DESCRIBE $services_table");
        $column_names = array_column($columns, 'Field');
        
        // Add category_id column if missing
        if (!in_array('category_id', $column_names)) {
            $result = $wpdb->query("ALTER TABLE {$services_table} ADD COLUMN `category_id` bigint(20) NULL AFTER `type`");
            if ($result !== false) {
                error_log("Added category_id column to services table");
            } else {
                error_log("Failed to add category_id column: " . $wpdb->last_error);
            }
        }
        
        // Add visibility column if missing
        if (!in_array('visibility', $column_names)) {
            $result = $wpdb->query("ALTER TABLE {$services_table} ADD COLUMN `visibility` enum('public', 'backend_only', 'disabled') DEFAULT 'public' AFTER `status`");
            if ($result !== false) {
                error_log("Added visibility column to services table");
            } else {
                error_log("Warning: Failed to add visibility column: " . $wpdb->last_error);
            }
        }
        
        // Add sort_order column if missing
        if (!in_array('sort_order', $column_names)) {
            $result = $wpdb->query("ALTER TABLE {$services_table} ADD COLUMN `sort_order` int(11) DEFAULT 0 AFTER `visibility`");
            if ($result !== false) {
                error_log("Added sort_order column to services table");
            } else {
                error_log("Warning: Failed to add sort_order column: " . $wpdb->last_error);
            }
        }
        
        // Add updated_at column if missing
        if (!in_array('updated_at', $column_names)) {
            $result = $wpdb->query("ALTER TABLE {$services_table} ADD COLUMN `updated_at` datetime NULL AFTER `created_at`");
            if ($result !== false) {
                error_log("Added updated_at column to services table");
            } else {
                error_log("Warning: Failed to add updated_at column: " . $wpdb->last_error);
            }
        }
    }
    
    /**
     * Fix orphaned category references
     */
    private function fixOrphanedCategoryReferences() {
        global $wpdb;
        
        $services_table = $wpdb->prefix . 'kc_services';
        $categories_table = $wpdb->prefix . 'kc_categories';
        
        // Check if categories table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$categories_table'") != $categories_table) {
            error_log("Categories table doesn't exist yet, skipping orphaned reference cleanup");
            return;
        }
        
        // Find services with invalid category references
        $orphaned_count = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM $services_table s 
            WHERE s.category_id IS NOT NULL 
            AND s.category_id > 0 
            AND s.category_id NOT IN (SELECT id FROM $categories_table)
        ");
        
        if ($orphaned_count > 0) {
            error_log("Found $orphaned_count services with orphaned category references");
            
            // Set orphaned category_id to NULL
            $result = $wpdb->query("
                UPDATE $services_table s 
                SET category_id = NULL 
                WHERE s.category_id IS NOT NULL 
                AND s.category_id > 0 
                AND s.category_id NOT IN (SELECT id FROM $categories_table)
            ");
            
            if ($result !== false) {
                error_log("Fixed $result orphaned category references");
            } else {
                error_log("Failed to fix orphaned category references: " . $wpdb->last_error);
            }
        } else {
            error_log("No orphaned category references found");
        }
    }
    
    /**
     * Map services to existing diagnostics category if available
     */
    private function mapToExistingCategories() {
        global $wpdb;
        
        $categories_table = $wpdb->prefix . 'kc_categories';
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Check if categories table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$categories_table'") != $categories_table) {
            error_log("Categories table doesn't exist, skipping category mapping");
            return;
        }
        
        // Find diagnostics category
        $diagnostics_category = $wpdb->get_row("
            SELECT id, name FROM $categories_table 
            WHERE module_type = 'service' 
            AND (name LIKE '%diagnostics%' OR slug LIKE '%diagnostics%' OR name LIKE '%diagnosis%')
            ORDER BY id ASC 
            LIMIT 1
        ");
        
        if ($diagnostics_category) {
            error_log("Found diagnostics category: {$diagnostics_category->name} (ID: {$diagnostics_category->id})");
            
            // Map services without category to diagnostics category
            $services_without_category = $wpdb->get_var("
                SELECT COUNT(*) FROM $services_table 
                WHERE category_id IS NULL OR category_id <= 0
            ");
            
            if ($services_without_category > 0) {
                $result = $wpdb->query($wpdb->prepare("
                    UPDATE $services_table 
                    SET category_id = %d 
                    WHERE category_id IS NULL OR category_id <= 0
                ", $diagnostics_category->id));
                
                if ($result !== false) {
                    error_log("Mapped $result services to diagnostics category");
                } else {
                    error_log("Failed to map services to diagnostics category: " . $wpdb->last_error);
                }
            } else {
                error_log("No services without category found");
            }
        } else {
            error_log("No diagnostics category found in existing data");
        }
    }
    
    /**
     * Clean up invalid data
     */
    private function cleanupInvalidData() {
        global $wpdb;
        
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Set negative or zero category_id values to NULL
        $result = $wpdb->query("UPDATE $services_table SET category_id = NULL WHERE category_id <= 0");
        if ($result !== false && $result > 0) {
            error_log("Cleaned up $result invalid category_id values");
        }
        
        // Ensure status is either 0 or 1
        $result = $wpdb->query("UPDATE $services_table SET status = 1 WHERE status IS NULL OR status NOT IN (0, 1)");
        if ($result !== false && $result > 0) {
            error_log("Fixed $result invalid status values");
        }
        
        // Get current columns to check if new columns exist
        $columns = $wpdb->get_results("DESCRIBE $services_table");
        $column_names = array_column($columns, 'Field');
        
        // Set default visibility for services with NULL visibility (if column exists)
        if (in_array('visibility', $column_names)) {
            $result = $wpdb->query("UPDATE $services_table SET visibility = 'public' WHERE visibility IS NULL");
            if ($result !== false && $result > 0) {
                error_log("Set default visibility for $result services");
            }
        }
        
        // Set default sort_order for services with NULL sort_order (if column exists)
        if (in_array('sort_order', $column_names)) {
            $result = $wpdb->query("UPDATE $services_table SET sort_order = 0 WHERE sort_order IS NULL");
            if ($result !== false && $result > 0) {
                error_log("Set default sort_order for $result services");
            }
        }
    }
    
    /**
     * Fix missing timestamps
     */
    private function fixMissingTimestamps() {
        global $wpdb;
        
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Check if updated_at column exists
        $columns = $wpdb->get_results("DESCRIBE $services_table");
        $column_names = array_column($columns, 'Field');
        
        if (in_array('updated_at', $column_names)) {
            // Update services without updated_at timestamp
            $result = $wpdb->query("
                UPDATE $services_table 
                SET updated_at = COALESCE(created_at, NOW()) 
                WHERE updated_at IS NULL
            ");
            
            if ($result !== false) {
                error_log("Updated $result services with missing updated_at timestamps");
            } else {
                error_log("Failed to update missing timestamps: " . $wpdb->last_error);
            }
        }
    }
    
    /**
     * Reverse the migration (optional - for testing)
     */
    public function down() {
        global $wpdb;
        
        error_log("ComprehensiveCategoriesFix migration rollback not implemented for production safety");
        // We don't implement rollback for production safety
        // The changes made are safe and improve data integrity
    }
}

// Auto-execute migration function for the migration system
function comprehensive_categories_fix_migration() {
    $migration = new ComprehensiveCategoriesFix();
    return $migration->up();
}
?>