<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTDLTestResult extends KCModel {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct('tdl_test_results');
    }

    /**
     * Get all test results with optional filtering
     *
     * @param array $args Optional arguments for pagination and filtering
     * @return array List of test results with pagination data
     */
    public function getResults($args = []) {
        global $wpdb;
        
        $defaults = [
            'page' => 1,
            'per_page' => 25,
            'clinic_id' => null,
            'patient_id' => null,
            'doctor_id' => null,
            'request_id' => null,
            'result_status' => null,
            'result_id' => null,
            'order_number' => null,
            'date_from' => null,
            'date_to' => null,
            'orderby' => 'result_date',
            'order' => 'DESC',
            'search' => '',
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['patient_id'])) {
            $where .= ' AND patient_id = %d';
            $params[] = (int)$args['patient_id'];
        }
        
        if (!empty($args['doctor_id'])) {
            $where .= ' AND doctor_id = %d';
            $params[] = (int)$args['doctor_id'];
        }
        
        if (!empty($args['request_id'])) {
            $where .= ' AND request_id = %d';
            $params[] = (int)$args['request_id'];
        }
        
        if (!empty($args['result_status'])) {
            $where .= ' AND result_status = %s';
            $params[] = sanitize_text_field($args['result_status']);
        }
        
        if (!empty($args['result_id'])) {
            $where .= ' AND result_id = %s';
            $params[] = sanitize_text_field($args['result_id']);
        }
        
        if (!empty($args['order_number'])) {
            $where .= ' AND order_number = %s';
            $params[] = sanitize_text_field($args['order_number']);
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND result_date >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND result_date <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Search functionality
        if (!empty($args['search'])) {
            $search = sanitize_text_field($args['search']);
            $where .= ' AND (result_id LIKE %s OR order_number LIKE %s OR physician_notes LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        // Handle ordering
        $orderby = $args['orderby'];
        $order = $args['order'];
        
        // Validate order parameters
        $valid_orderby = ['id', 'result_id', 'order_number', 'result_date', 'result_status', 'created_at', 'updated_at'];
        if (!in_array($orderby, $valid_orderby)) {
            $orderby = 'result_date';
        }
        
        $order = strtoupper($order);
        if ($order !== 'ASC' && $order !== 'DESC') {
            $order = 'DESC';
        }
        
        // First get total count for pagination
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        $total = (int)$wpdb->get_var($wpdb->prepare($count_query, $params));
        
        // Now get the actual results with pagination
        $limit = (int)$args['per_page'];
        $offset = (int)(($args['page'] - 1) * $limit);
        
        $query = "SELECT * FROM {$table_name} WHERE {$where} ORDER BY {$orderby} {$order} LIMIT %d OFFSET %d";
        $params[] = $limit;
        $params[] = $offset;
        
        $results = $wpdb->get_results($wpdb->prepare($query, $params), ARRAY_A);
        
        // Return both results and pagination data
        return [
            'results' => $results,
            'total' => $total,
            'page' => (int)$args['page'],
            'per_page' => (int)$args['per_page'],
            'total_pages' => ceil($total / $args['per_page'])
        ];
    }

    /**
     * Count total test results with optional filtering
     *
     * @param array $args Optional filter arguments
     * @return int Number of results
     */
    public function countResults($args = []) {
        global $wpdb;
        
        $defaults = [
            'clinic_id' => null,
            'patient_id' => null,
            'doctor_id' => null,
            'request_id' => null,
            'result_status' => null,
            'result_id' => null,
            'order_number' => null,
            'date_from' => null,
            'date_to' => null,
            'search' => '',
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['patient_id'])) {
            $where .= ' AND patient_id = %d';
            $params[] = (int)$args['patient_id'];
        }
        
        if (!empty($args['doctor_id'])) {
            $where .= ' AND doctor_id = %d';
            $params[] = (int)$args['doctor_id'];
        }
        
        if (!empty($args['request_id'])) {
            $where .= ' AND request_id = %d';
            $params[] = (int)$args['request_id'];
        }
        
        if (!empty($args['result_status'])) {
            $where .= ' AND result_status = %s';
            $params[] = sanitize_text_field($args['result_status']);
        }
        
        if (!empty($args['result_id'])) {
            $where .= ' AND result_id = %s';
            $params[] = sanitize_text_field($args['result_id']);
        }
        
        if (!empty($args['order_number'])) {
            $where .= ' AND order_number = %s';
            $params[] = sanitize_text_field($args['order_number']);
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND result_date >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND result_date <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Search functionality
        if (!empty($args['search'])) {
            $search = sanitize_text_field($args['search']);
            $where .= ' AND (result_id LIKE %s OR order_number LIKE %s OR physician_notes LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        $query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        
        return (int)$wpdb->get_var($wpdb->prepare($query, $params));
    }

    /**
     * Get a single test result by ID with related details
     *
     * @param int $result_id Result ID
     * @return array|false Result data or false if not found
     */
    public function getResultById($result_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $result_id = (int)$result_id;
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d", 
            $result_id
        );
        
        $result = $wpdb->get_row($query, ARRAY_A);
        
        if (!$result) {
            return false;
        }
        
        return $result;
    }

    /**
     * Get a test result by its external result_id
     *
     * @param string $external_result_id External result ID
     * @return array|false Result data or false if not found
     */
    public function getResultByExternalId($external_result_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $external_result_id = sanitize_text_field($external_result_id);
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE result_id = %s", 
            $external_result_id
        );
        
        return $wpdb->get_row($query, ARRAY_A);
    }

    /**
     * Get results for a specific order number
     *
     * @param string $order_number Order number
     * @return array Results data
     */
    public function getResultsByOrderNumber($order_number) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $order_number = sanitize_text_field($order_number);
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE order_number = %s ORDER BY result_date DESC", 
            $order_number
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }

    /**
     * Create a new test result
     *
     * @param array $result_data Result data
     * @return int|false ID of new result or false on failure
     */
    public function createResult($result_data) {
        if (empty($result_data['patient_id'])) {
            return false;
        }
        
        // Sanitize data
        $data_to_save = [
            'patient_id' => (int)$result_data['patient_id'],
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];
        
        // Optional fields
        if (isset($result_data['doctor_id'])) {
            $data_to_save['doctor_id'] = (int)$result_data['doctor_id'];
        }
        
        if (isset($result_data['clinic_id'])) {
            $data_to_save['clinic_id'] = (int)$result_data['clinic_id'];
        }
        
        if (isset($result_data['request_id'])) {
            $data_to_save['request_id'] = (int)$result_data['request_id'];
        }
        
        if (isset($result_data['order_number'])) {
            $data_to_save['order_number'] = sanitize_text_field($result_data['order_number']);
        }
        
        if (isset($result_data['result_id'])) {
            $data_to_save['result_id'] = sanitize_text_field($result_data['result_id']);
        }
        
        if (isset($result_data['result_date'])) {
            $data_to_save['result_date'] = sanitize_text_field($result_data['result_date']);
        } else {
            $data_to_save['result_date'] = current_time('mysql');
        }
        
        if (isset($result_data['result_status'])) {
            $data_to_save['result_status'] = sanitize_text_field($result_data['result_status']);
        } else {
            $data_to_save['result_status'] = 'received';
        }
        
        if (isset($result_data['physician_notes'])) {
            $data_to_save['physician_notes'] = sanitize_textarea_field($result_data['physician_notes']);
        }
        
        if (isset($result_data['result_data'])) {
            // For large JSON data, don't sanitize as it could break the structure
            $data_to_save['result_data'] = $result_data['result_data'];
        }
        
        $result = $this->insert($data_to_save);
        
        return $result;
    }

    /**
     * Update an existing test result
     *
     * @param int $result_id Result ID
     * @param array $result_data Result data to update
     * @return bool Success or failure
     */
    public function updateResult($result_id, $result_data) {
        $result_id = (int)$result_id;
        
        if (empty($result_id)) {
            return false;
        }
        
        // Check if result exists
        $existing = $this->getResultById($result_id);
        if (!$existing) {
            return false;
        }
        
        // Prepare data to update
        $data_to_update = ['updated_at' => current_time('mysql')];
        
        // Only include fields that are provided
        if (isset($result_data['patient_id'])) {
            $data_to_update['patient_id'] = (int)$result_data['patient_id'];
        }
        
        if (isset($result_data['doctor_id'])) {
            $data_to_update['doctor_id'] = (int)$result_data['doctor_id'];
        }
        
        if (isset($result_data['clinic_id'])) {
            $data_to_update['clinic_id'] = (int)$result_data['clinic_id'];
        }
        
        if (isset($result_data['result_status'])) {
            $data_to_update['result_status'] = sanitize_text_field($result_data['result_status']);
        }
        
        if (isset($result_data['physician_notes'])) {
            $data_to_update['physician_notes'] = sanitize_textarea_field($result_data['physician_notes']);
        }
        
        if (isset($result_data['result_date'])) {
            $data_to_update['result_date'] = sanitize_text_field($result_data['result_date']);
        }
        
        return $this->update($data_to_update, ['id' => $result_id]);
    }

    /**
     * Mark a result as reviewed
     *
     * @param int $result_id Result ID
     * @param int $reviewer_id User ID of reviewer
     * @param string $notes Optional physician notes
     * @return bool Success or failure
     */
    public function markAsReviewed($result_id, $reviewer_id, $notes = '') {
        $result_id = (int)$result_id;
        $reviewer_id = (int)$reviewer_id;
        
        if (empty($result_id) || empty($reviewer_id)) {
            return false;
        }
        
        // Check if result exists
        $existing = $this->getResultById($result_id);
        if (!$existing) {
            return false;
        }
        
        $data = [
            'result_status' => 'reviewed',
            'reviewed_by' => $reviewer_id,
            'reviewed_at' => current_time('mysql'),
            'updated_at' => current_time('mysql'),
        ];
        
        if (!empty($notes)) {
            $data['physician_notes'] = sanitize_textarea_field($notes);
        }
        
        return $this->update($data, ['id' => $result_id]);
    }
    
    /**
     * Get results statistics
     *
     * @param array $args Optional filter arguments
     * @return array Statistics about test results
     */
    public function getResultsStats($args = []) {
        global $wpdb;
        
        $defaults = [
            'clinic_id' => null,
            'date_from' => null,
            'date_to' => null,
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND result_date >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND result_date <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Get total count
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        $total = (int)$wpdb->get_var($wpdb->prepare($count_query, $params));
        
        // Get counts by status
        $status_query = "SELECT result_status, COUNT(*) as count FROM {$table_name} 
                         WHERE {$where} GROUP BY result_status";
        $status_counts = $wpdb->get_results($wpdb->prepare($status_query, $params), ARRAY_A);
        
        // Format status counts
        $status_stats = [
            'received' => 0,
            'processing' => 0,
            'reviewed' => 0,
            'flagged' => 0,
            'cancelled' => 0
        ];
        
        foreach ($status_counts as $status) {
            $status_stats[$status['result_status']] = (int)$status['count'];
        }
        
        // Get results by day for the date range
        $date_from = !empty($args['date_from']) ? $args['date_from'] : date('Y-m-d', strtotime('-30 days'));
        $date_to = !empty($args['date_to']) ? $args['date_to'] : date('Y-m-d');
        
        $params = [];
        if (!empty($args['clinic_id'])) {
            $where = 'clinic_id = %d AND';
            $params[] = (int)$args['clinic_id'];
        } else {
            $where = '';
        }
        
        $params[] = $date_from;
        $params[] = $date_to;
        
        $daily_query = $wpdb->prepare(
            "SELECT DATE(result_date) as date, COUNT(*) as count 
             FROM {$table_name} 
             WHERE {$where} result_date BETWEEN %s AND %s 
             GROUP BY DATE(result_date) 
             ORDER BY date ASC",
            $params
        );
        
        $daily_counts = $wpdb->get_results($daily_query, ARRAY_A);
        
        // Get counts of flagged abnormal results
        $items_table = $wpdb->prefix . 'kc_tdl_test_result_items';
        
        $params = [];
        if (!empty($args['clinic_id'])) {
            $where = "r.clinic_id = %d AND";
            $params[] = (int)$args['clinic_id'];
        } else {
            $where = "";
        }
        
        if (!empty($args['date_from'])) {
            $where .= " r.result_date >= %s AND";
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= " r.result_date <= %s AND";
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        $abnormal_query = $wpdb->prepare(
            "SELECT COUNT(*) FROM {$items_table} i
             JOIN {$table_name} r ON i.result_id = r.id
             WHERE {$where} i.abnormal_flag IS NOT NULL AND i.abnormal_flag != ''",
            $params
        );
        
        $abnormal_count = (int)$wpdb->get_var($abnormal_query);
        
        return [
            'total' => $total,
            'by_status' => $status_stats,
            'daily' => $daily_counts,
            'abnormal_count' => $abnormal_count
        ];
    }
    
    /**
     * Delete a test result including its items
     *
     * @param int $result_id Result ID
     * @return bool Success or failure
     */
    public function deleteResult($result_id) {
        global $wpdb;
        
        $result_id = (int)$result_id;
        
        if (empty($result_id)) {
            return false;
        }
        
        // First delete all related items
        $items_table = $wpdb->prefix . 'kc_tdl_test_result_items';
        $wpdb->delete($items_table, ['result_id' => $result_id]);
        
        // Then delete the result itself
        return $this->delete(['id' => $result_id]);
    }
}