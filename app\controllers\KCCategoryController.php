<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCCategory;
use App\models\KCService;

class KCCategoryController extends KCBase {

    public $db;
    private $request;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        parent::__construct();
    }

    public function index() {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        $module_type = $request_data['module_type'] ?? 'service';
        $status_filter = $request_data['status'] ?? null;

        $category_model = new KCCategory();

        // For backend, show public and backend_only categories with optional status filter
        $categories = $category_model->getBackendCategories($module_type, $status_filter);

        wp_send_json([
            'status' => true,
            'data' => $categories
        ]);
    }

    public function getPublicCategories() {
        $request_data = $this->request->getInputs();
        $module_type = $request_data['module_type'] ?? 'service';
        $clinic_id = $request_data['clinic_id'] ?? null;
        
        $category_model = new KCCategory();
        
        if ($clinic_id) {
            $categories = $category_model->getCategoriesWithServiceCount($module_type, $clinic_id, ['public']);
        } else {
            $categories = $category_model->getPublicCategories($module_type);
        }

        wp_send_json([
            'status' => true,
            'data' => $categories
        ]);
    }

    public function save() {
        // Only allow administrators and clinic_admins to save categories
        $user_role = $this->getLoginUserRole();
        if ($user_role !== 'administrator' && $user_role !== $this->getClinicAdminRole()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        $category_model = new KCCategory();

        // Validate required fields
        if (empty($request_data['name']) || empty($request_data['module_type'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Name and module type are required', 'kc-lang')
            ]);
        }

        // Generate slug
        $slug = $category_model->generateSlug(
            $request_data['name'], 
            $request_data['module_type'],
            $request_data['id'] ?? null
        );

        $data = [
            'name' => sanitize_text_field($request_data['name']),
            'slug' => $slug,
            'description' => sanitize_textarea_field($request_data['description'] ?? ''),
            'module_type' => sanitize_text_field($request_data['module_type']),
            'parent_id' => !empty($request_data['parent_id']) ? (int)$request_data['parent_id'] : null,
            'visibility' => in_array($request_data['visibility'] ?? 'public', ['public', 'backend_only', 'disabled']) 
                ? $request_data['visibility'] : 'public',
            'sort_order' => (int)($request_data['sort_order'] ?? 0),
            'status' => (int)($request_data['status'] ?? 1),
            'updated_at' => current_time('Y-m-d H:i:s')
        ];

        if (!empty($request_data['id'])) {
            // Update existing category
            $result = $category_model->update($data, ['id' => (int)$request_data['id']]);
            $message = esc_html__('Category updated successfully', 'kc-lang');
        } else {
            // Create new category
            $data['created_at'] = current_time('Y-m-d H:i:s');
            $result = $category_model->insert($data);
            $message = esc_html__('Category created successfully', 'kc-lang');
        }

        if ($result) {
            wp_send_json([
                'status' => true,
                'message' => $message
            ]);
        } else {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Failed to save category', 'kc-lang')
            ]);
        }
    }

    public function edit() {
        // Only allow administrators and clinic_admins to edit categories
        $user_role = $this->getLoginUserRole();
        if ($user_role !== 'administrator' && $user_role !== $this->getClinicAdminRole()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        
        if (empty($request_data['id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Category ID is required', 'kc-lang')
            ]);
        }

        $category_model = new KCCategory();
        $category = $category_model->get_by(['id' => (int)$request_data['id']], '=', true);

        if (!$category) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Category not found', 'kc-lang')
            ]);
        }

        wp_send_json([
            'status' => true,
            'data' => $category
        ]);
    }

    public function delete() {
        // Only allow administrators and clinic_admins to delete categories
        $user_role = $this->getLoginUserRole();
        if ($user_role !== 'administrator' && $user_role !== $this->getClinicAdminRole()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        
        if (empty($request_data['id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Category ID is required', 'kc-lang')
            ]);
        }

        $category_model = new KCCategory();
        
        // Check if category has children
        $children = $category_model->get_by(['parent_id' => (int)$request_data['id']]);
        if (!empty($children)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Cannot delete category with subcategories', 'kc-lang')
            ]);
        }

        // Check if category is used by services
        global $wpdb;
        $services_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}kc_services WHERE category_id = %d",
            (int)$request_data['id']
        ));

        if ($services_count > 0) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Cannot delete category that is used by services', 'kc-lang')
            ]);
        }

        $result = $category_model->delete(['id' => (int)$request_data['id']]);

        if ($result) {
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Category deleted successfully', 'kc-lang')
            ]);
        } else {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Failed to delete category', 'kc-lang')
            ]);
        }
    }

    public function getHierarchy() {
        $request_data = $this->request->getInputs();
        $module_type = $request_data['module_type'] ?? 'service';

        $category_model = new KCCategory();
        $hierarchy = $category_model->getCategoryHierarchy($module_type, ['public', 'backend_only']);

        wp_send_json([
            'status' => true,
            'data' => $hierarchy
        ]);
    }

    /**
     * Get categories with service counts for API
     */
    public function getCategoriesWithCounts() {
        $request_data = $this->request->getInputs();
        $module_type = $request_data['module_type'] ?? 'service';
        $clinic_id = $request_data['clinic_id'] ?? null;
        $include_disabled = $request_data['include_disabled'] ?? false;

        $category_model = new KCCategory();

        // Get visibility filter based on request
        $visibility_filter = ['public', 'backend_only'];
        if ($include_disabled) {
            $visibility_filter[] = 'disabled';
        }

        if ($clinic_id) {
            $categories = $category_model->getCategoriesWithServiceCount($module_type, $clinic_id, $visibility_filter);
        } else {
            $categories = $category_model->getBackendCategories($module_type);
        }

        wp_send_json([
            'status' => true,
            'data' => $categories,
            'message' => esc_html__('Categories retrieved successfully', 'kc-lang')
        ]);
    }

    /**
     * Get services by category ID
     */
    public function getServicesByCategory() {
        $request_data = $this->request->getInputs();
        $category_id = $request_data['category_id'] ?? null;
        $clinic_id = $request_data['clinic_id'] ?? null;
        $doctor_id = $request_data['doctor_id'] ?? null;

        if (!$category_id) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Category ID is required', 'kc-lang')
            ]);
        }

        $service_model = new KCService();
        $services = $service_model->getServicesByCategoryId($category_id, $clinic_id);

        // Filter by doctor if specified
        if ($doctor_id && $services) {
            $services = array_filter($services, function($service) use ($doctor_id) {
                return $service->doctor_id == $doctor_id;
            });
        }

        wp_send_json([
            'status' => true,
            'data' => array_values($services)
        ]);
    }
}