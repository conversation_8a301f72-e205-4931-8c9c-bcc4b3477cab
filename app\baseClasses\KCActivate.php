<?php

namespace App\baseClasses;

use App\controllers\KCPaymentController;
use App\models\KCAppointment;
use App\models\KCClinic;
use App\models\KCDoctorClinicMapping;
use App\models\KCService;
use WP_Error;
use WP_Query;
use function Clue\StreamFilter\fun;


class KCActivate extends KCBase {

    private $request;
    private $db;

	public static function activate() {

		// Migrate database and other data...
		self::migrateDatabase();

		// following function call is only for development purpose remove in production mode.
		(new self())->migratePermissions();
//		(new self())->addDefaultPosts();
		(new self())->addDefaultOptions();
		(new self())->addDefaultModuleConfig();
		(new self())->addAdministratorPermission();
		(new self())->tableAlterFiled();
        (new self())->createShortcodePage();
        (new self())->migrateSidebar();
	}
	public function init() {
        // self::migrateDatabase();
        $version = KIVI_CARE_VERSION;
        $prefix = KIVI_CARE_PREFIX;

        // Add SignatureRX link only to administrator dashboard sidebar
        add_filter('kivicare_administrator_dashboard_sidebar_data', [$this, 'addSignatureRXToSidebar'], 999, 1);

        $config_options = kc_get_multiple_option("
        '{$prefix}telemed_enable_added_to_service_table',
        '{$prefix}doctor_service_duration_column',
        '{$prefix}new-permissions-migrate{$version}',
        '{$prefix}permissions-migrate-1.0{$version}',
        '{$prefix}lang_option',
        '{$prefix}widgetSetting',
        '{$prefix}widget_order_list',
        '{$prefix}local_payment_status',
        '{$prefix}patient_mapping_table_1',
        '{$prefix}patient_review_table_1',
        '{$prefix}patient_mapping_table_1',
        '{$prefix}service_mapping_new_column',
        '{$prefix}payment_appointment_table_insert_1',
        '{$prefix}copyrightText_save',
        'is_lang_version_2.3.7',
        'kivicare_version_2_3_0',
        '{$prefix}logout_redirect',
        'is_kivicarepro_upgrade_lang',
        '{$prefix}email_appointment_reminder',
        '{$prefix}showServiceImageFirst',
        '{$prefix}tax_table_migrate',
        '{$prefix}custom_notification_dynamic_keys_update',
        '{$prefix}custom_form_table'
        ");

        // Restrict wp-admin access for non-admin roles
        add_action('admin_init', array($this, 'restrictNonAdminAccess'));

        // Redirect wp-login.php to custom login pages
        add_filter('login_url', array($this, 'redirectLoginPage'), 10, 3);

        // Block direct access to wp-login.php for plugin roles
        add_action('login_init', array($this, 'blockWpLoginAccess'));

        // Intercept all requests to wp-login.php before WordPress processes them
        add_action('init', array($this, 'interceptLoginRequests'));

        // Override login page templates to display in full screen
        add_filter('template_include', array($this, 'loadFullScreenLoginTemplate'), 999);

		add_action( 'set_logged_in_cookie', function( $logged_in_cookie ){
			$_COOKIE[LOGGED_IN_COOKIE] = $logged_in_cookie;
		} );

        //hook call to load text domain at proper time
        add_action( 'init', function (){
            //translate language
            load_plugin_textdomain( 'kc-lang', false, dirname( KIVI_CARE_BASE_NAME ) . '/languages' );
        }, 1); // Priority 1 to load early in init

        //security hook
        add_filter( 'xmlrpc_methods', function ( $methods ) {
            return array();
        } );

        //add user_status parameter in get_user function
		add_action( 'pre_user_query', function ( $query ) {
			if ( isset( $query->query_vars['user_status'] ) ) {
				$query->query_where .= " AND user_status = " . (int) $query->query_vars['user_status'];
			}
		} );

        add_filter( 'user_search_columns', function( $search_columns ) {
            $search_columns[] = 'display_name';
            $search_columns[] = 'user_status';
            return $search_columns;
        } );

        add_action('pre_get_posts', array($this, 'wpb_remove_products_from_shop_listing'), 90, 1);

        add_action('init',function () use ($config_options){
            // Legacy migration - disabled after services table merge
            if(empty($config_options[KIVI_CARE_PREFIX.'telemed_enable_added_to_service_table'])){
                // This migration is no longer needed after services table merge
                update_option(KIVI_CARE_PREFIX.'telemed_enable_added_to_service_table','yes');
            }
            $this->migrateDoctorServiceTableData();
            // Legacy migration - disabled after services table merge
            if(empty($config_options[KIVI_CARE_PREFIX.'doctor_service_duration_column'])){
                // This migration is no longer needed after services table merge
                update_option(KIVI_CARE_PREFIX.'doctor_service_duration_column','yes');
            }
        });

        add_action('admin_init', function () use ($config_options){
            $allRole = ['administrator',$this->getPatientRole(),
                $this->getDoctorRole(),
                $this->getReceptionistRole(),
                $this->getClinicAdminRole()];


            if (empty($config_options[KIVI_CARE_PREFIX . 'new-permissions-migrate' . KIVI_CARE_VERSION])) {

                $editable_roles = get_editable_roles();

                if(!empty($editable_roles) && is_array($editable_roles)){

                    $editable_roles = array_keys($editable_roles);
                    $containsSearch = count(array_intersect($allRole, $editable_roles)) === count($allRole);

                    if($containsSearch){
                        foreach ($allRole as $role) {
                            $subscriber = get_role($role);

                            $subscriber->add_cap('upload_files',kcGetUserDefaultPermission($subscriber,'upload_files',true));
                            $subscriber->add_cap('edit_published_pages',kcGetUserDefaultPermission($subscriber,'edit_published_pages',true));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'home_page', kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'home_page',$role === $this->getPatientRole()));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_report',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_report',true));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_report_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_report_add',$role !== $this->getPatientRole()));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_report_view',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_report_view',true));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_report_delete' ,kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_report_delete', $role !== $this->getPatientRole()));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_clinic', kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_clinic', $role === $this->getPatientRole()));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'appointment_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'appointment_export',true));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_encounter_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_encounter_export',true));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'prescription_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'prescription_export',true));
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_bill_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_bill_export',true));

                            // Add contacts directory permissions for all roles
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_list', true);
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_add', true);
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_edit', true);
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_delete', true);


                            if(in_array($role,['administrator', $this->getClinicAdminRole()])){
                                if($role === 'administrator'){
                                    $subscriber->add_cap(KIVI_CARE_PREFIX . 'clinic_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'clinic_export',true));
                                }
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'receptionist_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'receptionist_export',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'custom_field_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'custom_field_export',true));

                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'tax_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'tax_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'tax_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'tax_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'tax_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'tax_list',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'tax_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'tax_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'tax_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'tax_export',true));

                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'category_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'category_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'category_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'category_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'category_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'category_list',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'category_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'category_delete',true));

                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'custom_form_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'custom_form_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'custom_form_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'custom_form_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'custom_form_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'custom_form_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'custom_form_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'custom_form_list',true));

                                // Contact module permissions
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_list',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_view',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_view',true));
                            }

                            if(in_array($role,['administrator', $this->getClinicAdminRole(),$this->getReceptionistRole()])){
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'doctor_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'doctor_export',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_patient',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_patient',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_doctor',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_doctor',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_appointment',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_appointment',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_revenue',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_revenue',true));
                            }

                            if($role === $this->getDoctorRole()){
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_patient',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_patient',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_appointment',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_appointment',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_today_appointment',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_today_appointment',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'dashboard_total_service',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'dashboard_total_service',true));

                                // Contact module permissions for doctors
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_list',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_view',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'contact_view',true));
                            }

                            if($role !== $this->getPatientRole()){
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'doctor_session_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'doctor_session_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'doctor_session_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'doctor_session_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'doctor_session_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'doctor_session_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'doctor_session_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'doctor_session_list',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'doctor_session_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'doctor_session_export',true));

                                // Service session permissions
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'service_session_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'service_session_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'service_session_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'service_session_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'service_session_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'service_session_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'service_session_list',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'service_session_list',true));

                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'clinic_schedule_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'clinic_schedule_add',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'clinic_schedule_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'clinic_schedule_edit',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'clinic_schedule_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'clinic_schedule_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_export',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'service_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'service_export',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'clinic_schedule_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'clinic_schedule_export',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'static_data_export',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'static_data_export',true));
                            }

                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'static_data_add',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'static_data_add',$role !== $this->getPatientRole()));

                            do_action( 'kivicare_new_permissions_migrate', $role, $subscriber );

                        }
                        update_option(KIVI_CARE_PREFIX . 'new-permissions-migrate' . KIVI_CARE_VERSION, 'yes');
                    }
                }
            }

            if (empty($config_options[KIVI_CARE_PREFIX . 'permissions-migrate-1.0' . KIVI_CARE_VERSION])) {
                $editable_roles = get_editable_roles();
                if(!empty($editable_roles) && is_array($editable_roles)){
                    $editable_roles = array_keys($editable_roles);
                    $containsSearch = count(array_intersect($allRole, $editable_roles)) === count($allRole);
                    if($containsSearch){
                        foreach ($allRole as $role) {
                            $subscriber = get_role($role);
                            if($role === $this->getPatientRole()){
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_report_delete',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_report_delete',true));
                                $subscriber->add_cap(KIVI_CARE_PREFIX . 'patient_report_edit',kcGetUserDefaultPermission($subscriber,KIVI_CARE_PREFIX .'patient_report_edit',true));
                            }
                        }
                        update_option(KIVI_CARE_PREFIX . 'permissions-migrate-1.0' . KIVI_CARE_VERSION, 'yes');
                    }
                }
            }
        });

        add_filter( 'ajax_query_attachments_args',function( $query ) {
            if( in_array($this->getLoginUserRole(), [$this->getPatientRole(),
                $this->getDoctorRole(),
                $this->getReceptionistRole(),
                $this->getClinicAdminRole()]) ) {
                $user_id = get_current_user_id();
                if ( $user_id ) {
                    $query['author'] = $user_id;
                }
                return $query;
            }
            return $query;
        } );

		// Check language options is added in option table (deprecated language)
		if(empty($config_options[KIVI_CARE_PREFIX.'lang_option'])) {

            //condition to check user is new user or exists user
            update_option(KIVI_CARE_PREFIX.'new_user',1);

			$lang_option = [
				'lang_option' => [
					[
						'label' => 'English',
						'id' => 'en'
					],
					[
						'label' => 'Arabic',
						'id' => 'ar'
					],
					[
						'label' => 'Greek',
						'id' => 'gr'
					],
					[
						'label' => 'Franch',
						'id' => 'fr'
					],
					[
						'label' => 'Hindi',
						'id' => 'hi'
					]
				],
			];

            //add language option in options table
			add_option(KIVI_CARE_PREFIX.'lang_option', json_encode($lang_option));

		}

        //add default sidebar data
        add_action('init',function (){
            $this->sidebarData();
        });

        //new appointment shortcode default value add in options table
		if(empty($config_options[KIVI_CARE_PREFIX.'widgetSetting'])){
		    $this->widgetSettingLoad();
            $config_options[KIVI_CARE_PREFIX.'widgetSetting'] = get_option(KIVI_CARE_PREFIX.'widgetSetting');
        }

        //new appointment shortcode sidebar sequence order default value add in options table
        if(empty($config_options[KIVI_CARE_PREFIX.'widget_order_list'])){
            update_option(KIVI_CARE_PREFIX.'widget_order_list',kcDefaultAppointmentWidgetOrder());
        }

        //add local/offline payment value default enable in options table
        if(empty($config_options[KIVI_CARE_PREFIX.'local_payment_status'])){
            update_option(KIVI_CARE_PREFIX.'local_payment_status', 'on');
        }

        //enable loco translate if user is new and hide deprecated language module from dashboard
        if(kcToCheckUserIsNew()){
            update_option(KIVI_CARE_PREFIX.'locoTranslateState',1);
        }

        //add patient clinic table if not available
        if(empty($config_options[KIVI_CARE_PREFIX.'patient_mapping_table_1'])){
            update_option(KIVI_CARE_PREFIX.'patient_mapping_table_1','yes');
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
            require KIVI_CARE_DIR . 'app/database/kc-patient-clinic-mapping-db.php';
        }

        //add patient clinic table if not available
        if(empty($config_options[KIVI_CARE_PREFIX.'patient_review_table_1'])){
            update_option(KIVI_CARE_PREFIX.'patient_review_table_1','yes');
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
            require KIVI_CARE_DIR . 'app/database/kc-patient-review-db.php';
        }

        // Create contacts table if it doesn't exist
        if(empty($config_options[KIVI_CARE_PREFIX.'contacts_table'])){
            update_option(KIVI_CARE_PREFIX.'contacts_table','yes');
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
            require KIVI_CARE_DIR . 'app/database/kc-contacts-db.php';
        }

        add_action('init',function() use ($config_options){
            if(empty($config_options[KIVI_CARE_PREFIX.'patient_mapping_table_1'])){
                global $wpdb;
                kcUpdateFields($wpdb->prefix.'kc_patient_review',['review_description' => 'longtext',
                    'created_at' => 'datetime NOT NULL DEFAULT current_timestamp()',
                    'updated_at' => 'datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()']);
                update_option(KIVI_CARE_PREFIX.'patient_mapping_table_1','yes');
            }
            // Legacy migration - disabled after services table merge
            if(empty($config_options[KIVI_CARE_PREFIX.'service_mapping_new_column'])){
                // This migration is no longer needed after services table merge
                update_option(KIVI_CARE_PREFIX.'service_mapping_new_column','yes');
            }
        });

        //add PayPal payment method  and appointment mappings table
        if(empty($config_options[KIVI_CARE_PREFIX.'payment_appointment_table_insert_1'])){
            update_option(KIVI_CARE_PREFIX.'payment_appointment_table_insert_1','yes');
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
            require KIVI_CARE_DIR.'app/database/kc-payments-appointment-mapping-db.php';
        }

        //add default copyright text of dashboard
        if(empty($config_options[KIVI_CARE_PREFIX.'copyrightText_save'])){
            update_option(KIVI_CARE_PREFIX.'copyrightText_save','yes');
            // Delay translation until text domain is loaded
            add_action('init', function() {
                if (!get_option(KIVI_CARE_PREFIX.'copyrightText')) {
                    update_option(KIVI_CARE_PREFIX.'copyrightText', __('Medroid (EHR)','kc-lang'));
                }
            });
        }

        // add new language translation key in 2.2.8 (deprecated)
        if ( is_admin() && empty( $config_options['is_lang_version_2.3.7'])) {
            add_option('is_lang_version_2.3.7', 1);
            $this->getLangCopy();
            $this->mergeJson();
        }

        // kivicare version 2.3.0
        if ( is_admin() && empty( $config_options['kivicare_version_2_3_0'])) {
            add_option('kivicare_version_2_3_0', 1);
            update_option(KIVI_CARE_PREFIX.'request_helper_status', 'off');
        }

        //add default logout redirect page url in options table
        if(empty( $config_options[KIVI_CARE_PREFIX.'logout_redirect'])){
            // Get the page IDs for custom login pages
            $login_page_id = get_option('kivicare_register_login_page_id');
            $patient_login_page_id = get_option('kivicare_patient_login_page_id');

            // Get the URLs for these pages
            $login_url = $login_page_id ? get_permalink($login_page_id) : wp_login_url();
            $patient_login_url = $patient_login_page_id ? get_permalink($patient_login_page_id) : wp_login_url();

            // Update redirect options
            update_option(KIVI_CARE_PREFIX . 'logout_redirect', [
                "clinic_admin" => $login_url,
                "patient" => $patient_login_url,
                "receptionist" => $login_url,
                "doctor" => $login_url
            ]);
        }

        // (new self())->versionUpgradePatches();

        //check if request is kivicare dashboard
		if (isset($_REQUEST['page']) && $_REQUEST['page'] === "dashboard") {
            $this->dashboardPage();
		}

		// Enqueue/register Front-end assets
		add_action( 'wp_enqueue_scripts', array($this,'enqueueFrontScripts'));

        // Append meta tags to header...
		add_action( 'wp_head', array($this,'appendToHeader') );
		add_action( 'admin_head', array($this,'appendToHeader') );

        // Enable route classes
        (new KCRoutesHandler('App\\controllers\\'))->init();

        //call shortcode init class
        ( new WidgetHandler )->init();

		(new self())->load_plugin();

		//hook to add plugin  sidebar menu in WordPress dashboard
		add_action( 'admin_menu', array($this, 'adminMenu'));

        //hook to remove WordPress icon from WordPress dashboard
        add_action( 'wp_before_admin_bar_render', array($this, 'kc_remove_logo_wp_admin'), 0 );

        //add kivicare widget category in gutenberg
        add_filter( 'block_categories_all', array($this,'addBlockCategories'), 10, 2 );

        //add shortcode gutenberg widget js/css assets
        add_action( 'enqueue_block_editor_assets', array($this, 'kivicareShortcodeWidgetBlock'));

		// hook to set email header...
		add_filter( 'wp_mail_content_type', function() {
            return 'text/html';
        } );

		// hook to check login is allowed by admin  if not  return error response
		add_filter( 'authenticate', array($this, 'validateAuthUser'), 20, 3 );

		// Redirect user to dashboard after login
		add_filter( 'login_redirect', array($this, 'redirectUserToDashboard'), 10, 3 );

        //hook to restrict woocommerce agent
		add_filter( 'woocommerce_prevent_admin_access', array($this, 'kivicare_agent_admin_access') , 20, 1 );

        //hook to enqueue wordpress login page script...
		add_action( 'login_enqueue_scripts', array($this, 'loginPageStyles'), 11 );

		// Hide admin bar...
		add_action('after_setup_theme', array($this, 'removeAdminBar'));

        //update plugin language file (deprecated language)
		if ( is_admin() && empty( $config_options['is_kivicarepro_upgrade_lang'])) {
			add_option('is_kivicarepro_upgrade_lang', 1);
			$this->updateLangFile();
		}

        //kivicare elementor widget register and add doctor and clinic widget
        add_action( 'elementor/elements/categories_registered', [$this,'kcElementerCategoryRegistered'] );
        add_action( 'elementor/widgets/widgets_registered', [$this,'kcAddElementorWidget']);

        //add patient appointment reminder cron job
        if (!empty($config_options[KIVI_CARE_PREFIX . 'email_appointment_reminder'])) {
            $appointment_reminder_data = unserialize($config_options[KIVI_CARE_PREFIX . 'email_appointment_reminder']);
            if( isset($appointment_reminder_data['time']) && ((isset($appointment_reminder_data['status'])
            && $appointment_reminder_data['status'] == 'on') || (isset($appointment_reminder_data['sms_status'])
            && $appointment_reminder_data['sms_status'] =='on') || (isset($appointment_reminder_data['whatapp_status'])
            && $appointment_reminder_data['whatapp_status'] == 'on'))) {
                kcAddCronJob('kivicare_patient_appointment_reminder','patientAndDoctorAppointmentReminder');
            }
        }

        //hook to change old langauge translate to loco translate from language error page (resources/views/kc_notice.php)
        add_action( 'template_redirect', [ $this, 'kcEnableLocoTranslate' ] );

        //hook to register plugin custom posttype (sms/email/calendar and googlemeet)
        add_action('init',[ $this, 'addCustomPostType' ]);

        //hook to change WordPress login page icon
        if(kcWordpressLogostatusAndImage('status')){
            add_action( 'login_enqueue_scripts', [$this,'kcChangeWordpressLogo'] );
        }

        // hook to check login is valid user  if not  return error response
        add_filter( 'wp_authenticate_user', [$this,'kccheckActiveUser'],10,2);

        // Add a post display state for special kivicare pages.
        add_filter( 'display_post_states', array( $this, 'add_display_post_states' ), 10, 2 );

        add_action('elementor/editor/before_enqueue_scripts', function (){
            // Add cache busting with current time
            $cache_bust = time();
            wp_enqueue_style( 'kc_font_awesome', $this->plugin_url . 'assets/css/font-awesome-all.min.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust, false);
        });

        if(!isKiviCareProActive()){
            add_filter( "plugin_action_links_".KIVI_CARE_BASE_NAME, function ( $links ) {
                // Build and escape the URL.
                $url = esc_url( add_query_arg(
                    'page',
                    'kc_go_pro_settings_link',
                    'https://codecanyon.net/item/kivicare-pro-clinic-patient-management-system-ehr-addon/30690654'
                ) );
                // Create the link.
                $settings_link = "<a href='".esc_url($url)."' class='kivicare-plugin-gopro' target='_blank' style='color: #93003c;'><b>".esc_html__('Go Pro','kc-lang')."</b></a>";
                // Adds the link to the end of the array.
                $links[] = $settings_link;
                return $links;
            } );
        }

        if(empty($config_options[KIVI_CARE_PREFIX.'showServiceImageFirst'])){
            if(empty($config_options[KIVI_CARE_PREFIX.'widgetSetting'])){
                // $widgetSettingTest = json_decode($config_options[KIVI_CARE_PREFIX.'widgetSetting'], true)??[];
                $widgetSettingTest = [];
                $showServiceImage1 = ['showServiceImage'=>1];
                $KCfinalArray = $widgetSettingTest + $showServiceImage1;

                update_option(KIVI_CARE_PREFIX.'widgetSetting', json_encode($KCfinalArray));
                update_option(KIVI_CARE_PREFIX.'showServiceImageFirst', 'added');
            }
        }

        $this->woocommerce_shop_order_add_column();

        //add patient clinic table if not available
        if(empty($config_options[KIVI_CARE_PREFIX.'tax_table_migrate'])){
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
            require KIVI_CARE_DIR . 'app/database/kc-tax-db.php';
            require KIVI_CARE_DIR . 'app/database/kc-tax-data-db.php';
            update_option(KIVI_CARE_PREFIX.'tax_table_migrate','yes');
        }

        if(empty($config_options[KIVI_CARE_PREFIX.'custom_notification_dynamic_keys_update'])){
            delete_option('custom_notification_dynamic_keys');
            update_option(KIVI_CARE_PREFIX.'custom_notification_dynamic_keys_update', [
                ['key' => '[api_key]', 'value' => ''],
                ['key' => '[sender_number]', 'value' => ''],
                ['key' => '[receiver_number]', 'value' => '[dynamic_receiver_number]'],
                ['key' => '[sms_body]', 'value' => '[dynamic_sms_body]']
            ]);
        }

        if(empty($config_options[KIVI_CARE_PREFIX.'custom_form_table'])){
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
            require KIVI_CARE_DIR . 'app/database/kc-custom-form-db.php';
            require KIVI_CARE_DIR . 'app/database/kc-custom-form-data-db.php';
            update_option(KIVI_CARE_PREFIX.'custom_form_table','yes');
        }


        add_action('wp_ajax_kc_attachment',function(){

            if ( ! kcCheckPermission( 'patient_report_view' ) ) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            $c = base64_decode($_REQUEST['key']);
            $ivlen = openssl_cipher_iv_length($cipher="AES-128-CBC");
            $iv = substr($c, 0, $ivlen);
            $hmac = substr($c, $ivlen, $sha2len=32);
            $ciphertext_raw = substr($c, $ivlen+$sha2len);
            $attachment_id = openssl_decrypt($ciphertext_raw, $cipher, AUTH_KEY, $options=OPENSSL_RAW_DATA, $iv);
            $calcmac = hash_hmac('sha256', $ciphertext_raw, AUTH_KEY, $as_binary = true);
            if (hash_equals($hmac, $calcmac)) // timing attack safe comparison
            {
                if ($file_url = get_attached_file( (int)$attachment_id )) {

                    header('Content-Description: File Transfer');
                    header("Content-type:".get_post_mime_type($attachment_id ));
                    header('Content-Disposition: filename="'.basename($file_url).'"');
                    header('Expires: 0');
                    header('Cache-Control: must-revalidate');
                    header('Pragma: public');
                    header('Content-Length: ' . filesize($file_url));


                    while (ob_get_level()) {
                        ob_end_clean();
                    }
                    readfile($file_url);
                    exit();
                } else {
                    echo "Invalid attachment or attachment not found.";
                }
            }
            die;
        });

        add_filter( 'woocommerce_login_redirect', [$this,'custom_redirect_after_password_reset'], 20, 2 );

        add_action('wp_enqueue_scripts', [$this,'enqueue_kivicare_tailwind_styles']);
        add_action('init', function (){
            if (is_plugin_active('paid-memberships-pro/paid-memberships-pro.php')) {
                if(!class_exists('KC_PMP_Extented')){
                    require KIVI_CARE_DIR . 'utils/KC_PMP_Extented.php';
                }
                new \KC_PMP_Extented();
            }
        });

        // Add Contacts Directory to sidebar menus - run after init to ensure all hooks are registered
        add_action('init', function() {
            // Run on init with a priority of 99 to ensure it runs after the sidebar data is loaded
            add_filter('kivicare_administrator_dashboard_sidebar_data', [$this,'kc_add_contact_to_sidebar'], 99);
            add_filter('kivicare_clinic_admin_dashboard_sidebar_data', [$this,'kc_add_contact_to_sidebar'], 99);
            add_filter('kivicare_doctor_dashboard_sidebar_data', [$this,'kc_add_contact_to_sidebar'], 99);
            add_filter('kivicare_receptionist_dashboard_sidebar_data', [$this,'kc_add_contact_to_sidebar'], 99);
        }, 20);

        add_action('admin_init',function(){
            if (!empty(get_option(KIVI_CARE_PREFIX . 'contact-permissions-migrate'))) return ;

             // Define all roles
            $allRoles = [
                'administrator',
                $this->getDoctorRole(),
                $this->getReceptionistRole(),
                $this->getClinicAdminRole()
            ];

            // Get all editable roles in WordPress
            $editable_roles = get_editable_roles();

            if (!empty($editable_roles) && is_array($editable_roles)) {
                // Get the role names
                $editable_roles = array_keys($editable_roles);

                // Check if all our required roles exist
                $containsSearch = count(array_intersect($allRoles, $editable_roles)) === count($allRoles);

                if ($containsSearch) {
                    // Add contact capabilities to each role
                    foreach ($allRoles as $role) {
                        $subscriber = get_role($role);

                        if ($subscriber) {
                            // Add contacts directory permissions for all roles
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_list', true);
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_add', true);
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_edit', true);
                            $subscriber->add_cap(KIVI_CARE_PREFIX . 'contact_delete', true);
                        }
                    }

                    // Update option to indicate migration has been completed
                    update_option(KIVI_CARE_PREFIX . 'contact-permissions-migrate', 'yes');
                }
            }
        });


    }
    /**
     * Add Contacts Directory to sidebar
     *
     * @param array $sidebar_data
     * @return array
     */
    public function kc_add_contact_to_sidebar($sidebar_data) {
        // Create new menu item for Contacts Directory
        $new_item = [
            'label' => 'Contacts',
            'type' => 'route',
            'link' => 'contacts-directory',
            'iconClass' => 'fa fa-address-book',
            'routeClass' => 'contact_list',
        ];

        // Find a good position to insert (before Reports or Billing)
        $insert_position = count($sidebar_data) - 3;
        if ($insert_position < 0) {
            $insert_position = count($sidebar_data);
        }

        // Insert the contact module menu item
        array_splice($sidebar_data, $insert_position, 0, [$new_item]);

        return $sidebar_data;
    }
    function enqueue_kivicare_tailwind_styles() {
        $css_url = KIVI_CARE_DIR_URI . 'assets/css/tailwind.min.css';
        // Add cache busting with current time
        $cache_bust = time();

        wp_enqueue_style(
            'kivicare-tailwind',
            $css_url,
            [],
            '1.0.0-' . $cache_bust
        );
    }

    public function custom_redirect_after_password_reset( $redirect_to, $user ) {
        // Check if the user is coming from the password reset form
        if (in_array( KIVI_CARE_PREFIX . "patient", $user->roles )) {
            $redirect_to = kcGetLogoinRedirectSetting('patient');
            return $redirect_to;
        }
    }

    public function wpb_remove_products_from_shop_listing(WP_Query $query)
    {
        if (is_admin()) {
            return $query;
        }

        if ($query->get('post_type') !== 'product') {
            return $query;
        }

        global $wpdb;
        //  phpcs:ignore WordPress.DB.DirectDatabaseQuery.DirectQuery
        $kivicare_products = $wpdb->get_col("SELECT DISTINCT `posts`.`ID` FROM `{$wpdb->posts}` AS `posts`
                INNER JOIN `{$wpdb->postmeta}` AS `meta` ON ( `posts`.`ID` = `meta`.`post_id` AND `meta`.`meta_key` = 'kivicare_service_id' )
                WHERE `posts`.`post_type` = 'product'
                AND `posts`.`post_status` = 'publish'");

        if (!$kivicare_products) {
            return $query;
        }

        $post__no_in = (array) $query->get('post__not_in');

        $query->set('post__not_in', array_merge($post__no_in, $kivicare_products));

        return $query;
    }

    public function widgetSettingLoad(){

        $data  = [
            'showClinicImage' => '1',
            'showClinicAddress' => '1',
            'clinicContactDetails' => [
                'id' => '3',
                'label' => 'Show email address'
            ],
            'showDoctorImage' => '1',
            'showDoctorExperience' => '1',
            'doctorContactDetails' => [
                'id' => '3',
                'label' => 'Show email address'
            ],
            'showDoctorSpeciality' => '1',
            'showDoctorDegree' => '1',
            'showDoctorRating' => '1',
            'showServiceImage' => '1',
            'showServicetype' => '1',
            'showServicePrice' => '1',
            'showServiceDuration' => '1',
            'primaryColor' => '#7093e5',
            'primaryHoverColor' => '#4367b9',
            'secondaryColor' => '#f68685',
            'secondaryHoverColor' => '#df504e',
            'widget_print' => '1',
            'afterWoocommerceRedirect' => '1',
        ];

        update_option(KIVI_CARE_PREFIX.'widgetSetting', json_encode($data));
    }

    public function dashboardPage(){
        //filter to update browser tab title
        add_filter( 'admin_title', function ( $admin_title, $title ) {
            return get_bloginfo('name');
        }, 10, 2 );

        // Enqueue Admin-side assets...
        add_action( 'admin_enqueue_scripts', array($this,'enqueueStyles'));
        add_action( 'admin_enqueue_scripts', array($this,'enqueueScripts'));
        add_action( 'admin_enqueue_scripts', function (){
            global $wp_scripts, $wp_styles;
            // Loop through the registered scripts
            foreach ( $wp_scripts->registered as $handle => $script ) {
                // Check if the script was registered by another plugin
                if ( strpos( $script->src??"", '/plugins/' ) !== false && strpos( $script->src??"", '/kivicare-clinic-management-system/' ) === false ) {
                    // Unregister the script
                    if(!in_array($handle,['kcrp_razorpay_payment','kivicare_razorpay_checkout'])){
                        wp_deregister_script( $handle );
                    }
                }
            }

            // Loop through the registered styles
            foreach ( $wp_styles->registered as $handle => $style ) {
                // Check if the style was registered by another plugin
                if ( strpos( $style->src, '/plugins/' ) !== false && strpos( $style->src, '/kivicare-clinic-management-system/' ) === false ) {
                    // Unregister the style
                    wp_deregister_style( $handle );
                }
            }
        },PHP_INT_MAX);

        // hook to remove hide sidebar and top-bar...
        add_action('admin_head', array($this, 'hideSideBar'));

        //language translate object pass by window variable
        add_action( 'admin_head', array($this,'appendLanguageInHead') );

        // Increase the heartbeat interval to 60 seconds
        add_filter('heartbeat_settings', function ($settings) {
            $settings['interval'] = 60;
            return $settings;
        });

        // Disable WordPress auth check modal to prevent session timeout popup issues
        add_action('admin_init', function() {
            // Remove the auth check functionality that shows the login popup
            remove_action('admin_enqueue_scripts', 'wp_auth_check_load');
            remove_action('wp_enqueue_scripts', 'wp_auth_check_load');
        });

        // Completely disable WordPress auth check on Kivicare pages
        add_filter('wp_auth_check_load', array($this, 'disableWpAuthCheck'));

        // Disable auth check HTML output
        add_action('admin_footer', array($this, 'removeAuthCheckHtml'), 1);
        add_action('wp_footer', array($this, 'removeAuthCheckHtml'), 1);

        // Add custom session timeout handling
        add_action('admin_enqueue_scripts', array($this, 'enqueueSessionTimeoutHandler'));
        add_action('wp_enqueue_scripts', array($this, 'enqueueSessionTimeoutHandler'));
    }

	public function load_plugin () {
		if ( is_admin() && !get_option( 'is_upgrade_2.1.5')) {
            add_option('is_upgrade_2.1.5', 0);
            require_once ABSPATH . '/wp-admin/includes/upgrade.php';
			require KIVI_CARE_DIR . 'app/database/kc-custom-field-data-db.php';
		}
	}
	public function tableAlterFiled(){
        global $wpdb;
        $table_patient_encounter = $wpdb->prefix . 'kc_patient_encounters';
        $new_fields = [
            'clinic_id' => 'bigint',
        ];

        //add new column in existing table
        kcUpdateFields($table_patient_encounter,$new_fields);

        $table_billing = $wpdb->prefix . 'kc_bills';

        //add new column in existing table
        kcUpdateFields($table_billing,$new_fields);

        $table_clinics = $wpdb->prefix . 'kc_clinics';
        $new_fields_clinics = [
            'country_code' => 'varchar(10)',
            'country_calling_code' => 'varchar(10)',
        ];
        //add new column in existing table
        kcUpdateFields($table_clinics, $new_fields_clinics);

        //alter medical_report_table name field
		if(!get_option( KIVI_CARE_PREFIX.'medical_report_table_name_alter') ) {

            $medical_report_table_name = $wpdb->prefix . 'kc_patient_medical_report';

            if ($wpdb->get_var("SHOW TABLES LIKE '$medical_report_table_name'") == $medical_report_table_name) {
                $wpdb->query("ALTER TABLE $medical_report_table_name MODIFY COLUMN name TEXT;");
            }

            update_option(KIVI_CARE_PREFIX.'medical_report_table_name_alter', 'yes');
        }

        if ( ! get_option( KIVI_CARE_PREFIX . 'medical_history_table_name_alter' ) ) {

            $medical_history_table_name = $wpdb->prefix . 'kc_medical_history';

            if ($wpdb->get_var("SHOW TABLES LIKE '$medical_history_table_name'") == $medical_history_table_name) {
                $wpdb->query( "ALTER TABLE $medical_history_table_name MODIFY title TEXT" );
            }

            update_option( KIVI_CARE_PREFIX . 'medical_history_table_name_alter', 'yes' );
        }

        if ( ! get_option( KIVI_CARE_PREFIX . 'service_table_column_alter' ) ) {

            $table_services = $wpdb->prefix . 'kc_services';

            $new_fields_services = [
                'price' => 'varchar(50)',
            ];

            if ($wpdb->get_var("SHOW TABLES LIKE '$table_services'") == $table_services) {
                //modify column in existing table
                kcUpdateFieldsDataType($table_services, $new_fields_services);
            }

            update_option( KIVI_CARE_PREFIX . 'service_table_column_alter', 'yes' );
        }

        if ( ! get_option( KIVI_CARE_PREFIX . 'bill_table_column_alter' ) ) {

            $new_fields_bills = [
                'total_amount' => 'varchar(50)',
                'discount' => 'varchar(50)',
                'actual_amount' => 'varchar(50)',
            ];

            if ($wpdb->get_var("SHOW TABLES LIKE '$table_billing'") == $table_billing) {
                //modify column in existing table
                kcUpdateFieldsDataType($table_billing, $new_fields_bills);
            }

            update_option( KIVI_CARE_PREFIX . 'bill_table_column_alter', 'yes' );
        }

        // Legacy migration - disabled after services table merge
        if ( ! get_option( KIVI_CARE_PREFIX . 'service_doctor_mapping_table_column_alter' ) ) {
            // This migration is no longer needed after services table merge
            update_option( KIVI_CARE_PREFIX . 'service_doctor_mapping_table_column_alter', 'yes' );
        }
        if ( ! get_option( KIVI_CARE_PREFIX . 'bill_items_table_column_alter' ) ) {

            $table_bill_items = $wpdb->prefix . 'kc_bill_items';

            $new_fields_bill_items = [
                'price' => 'varchar(50)',
            ];

            if ($wpdb->get_var("SHOW TABLES LIKE '$table_bill_items'") == $table_bill_items) {
                //modify column in existing table
                kcUpdateFieldsDataType($table_bill_items, $new_fields_bill_items);
            }

            update_option( KIVI_CARE_PREFIX . 'bill_items_table_column_alter', 'yes' );
        }

        if ( ! get_option( KIVI_CARE_PREFIX . 'static_data_table_column_alter' ) ) {

            $table_static_data = $wpdb->prefix . 'kc_static_data';

            $new_fields_static_data = [
                'label' => 'text',
                'value' => 'text',
            ];

            if ($wpdb->get_var("SHOW TABLES LIKE '$table_static_data'") == $table_static_data) {
                //modify column in existing table
                kcUpdateFieldsDataType($table_static_data, $new_fields_static_data);
            }

            update_option( KIVI_CARE_PREFIX . 'static_data_table_column_alter', 'yes' );
        }
        if ( ! get_option( KIVI_CARE_PREFIX . 'prescription_table_column_alter' ) ) {

            $table_prescription = $wpdb->prefix . 'kc_prescription';

            $new_fields_prescription = [
                'name' => 'text',
            ];

            if ($wpdb->get_var("SHOW TABLES LIKE '$table_prescription'") == $table_prescription) {
                //modify column in existing table
                kcUpdateFieldsDataType($table_prescription, $new_fields_prescription);
            }

            update_option( KIVI_CARE_PREFIX . 'prescription_table_column_alter', 'yes' );
        }
        if ( !get_option( KIVI_CARE_PREFIX . 'add_buffer_time_table_column_alter' ) ) {

            $table_clinic_sessions = $wpdb->prefix . 'kc_clinic_sessions';

            $new_fields_clinic_sessions = [
                'buffertime' => 'int(5)',
            ];


            if ($wpdb->get_var("SHOW TABLES LIKE '$table_clinic_sessions'") == $table_clinic_sessions) {
                //modify column in existing table
                kcUpdateFields($table_clinic_sessions, $new_fields_clinic_sessions);
            }

            update_option( KIVI_CARE_PREFIX . 'add_buffer_time_table_column_alter', 'yes' );
        }

        if ( !get_option( KIVI_CARE_PREFIX . 'add_clinic_allow_doctor_limit' ) ) {
            $table_clinics = $wpdb->prefix . 'kc_clinics';
            $new_fields_clinics = [
                'allow_no_of_doc' => 'varchar(10)'
            ];
            //add new column in existing table
            kcUpdateFields($table_clinics, $new_fields_clinics);

            update_option( KIVI_CARE_PREFIX . 'add_clinic_allow_doctor_limit', 'yes' );
        }



    }
	public function updateLangFile(){

        //deprecated language module
		$temp_file = KIVI_CARE_DIR_URI.'resources/assets/lang/temp.json';

		$dir_name = KIVI_CARE_PREFIX.'lang';

		$upload_dir = wp_upload_dir();
		$user_dirname = $upload_dir['basedir'] . '/' . $dir_name;
		$get_user_lang = get_option(KIVI_CARE_PREFIX.'lang_option');
		$data = json_decode($get_user_lang,true);
		if(!file_exists($user_dirname)) {
			wp_mkdir_p( $user_dirname );
		}
		foreach ($data['lang_option'] as $key => $value) {
			$current_file = KIVI_CARE_DIR_URI.'resources/assets/lang/'.$value['id'].'.json';
			$old_file  = $user_dirname.'/'.$value['id'].'.json';
			if(!file_exists($old_file)){
				$data = file_get_contents($current_file);
				file_put_contents($user_dirname.'/'.$value['id'].'.json', $data);
				chmod($old_file, 0777);
			}else{
				$old_file_contente = file_get_contents($old_file);
				$new_file = KIVI_CARE_DIR_URI.'resources/assets/lang/en.json';
				$new_file_contente = file_get_contents($new_file);
				file_put_contents($user_dirname.'/'.$value['id'].'.json', json_encode(array_merge(json_decode($new_file_contente, true),json_decode($old_file_contente, true))));
			}
		}
		if(!file_exists($temp_file)){
			$media_temp = $user_dirname.'/temp.json';
			$temp_file_data = file_get_contents($temp_file);
			file_put_contents($user_dirname.'/temp.json', $temp_file_data);
			chmod($media_temp, 0777);
		}
	}
	public function validateAuthUser( $user ) {
        // check login user status
		if( isset($user->data->user_status) && (int)$user->data->user_status === 4 ) {
			$error = new WP_Error();
			$error->add( 403, __('Login has been disabled. please contact you system administrator. ','kc-lang') );
			return $error;
		}
		return $user;
	}
	public function removeAdminBar() {
		//hide admin bar if user is not admin
        if (!current_user_can('administrator') && !is_admin()) {
			show_admin_bar(false);
		}
	}

	/**
	 * Enqueue session timeout handler script and styles
	 */
	public function enqueueSessionTimeoutHandler() {
		// Only add this on Kivicare pages
		if (!$this->isKivicarePage()) {
			return;
		}

		// Add cache busting with current time
		$cache_bust = time();

		// Enqueue the session timeout handler CSS
		wp_enqueue_style(
			'kivicare-session-timeout-handler',
			$this->plugin_url . 'assets/css/session-timeout-handler.css',
			array(),
			KIVI_CARE_VERSION . '-' . $cache_bust
		);

		// Enqueue the session timeout handler script
		wp_enqueue_script(
			'kivicare-session-timeout-handler',
			$this->plugin_url . 'assets/js/session-timeout-handler.js',
			array('jquery'),
			KIVI_CARE_VERSION . '-' . $cache_bust,
			true
		);

		// Localize script with necessary data
		wp_localize_script('kivicare-session-timeout-handler', 'kivicareSessionConfig', array(
			'ajaxurl' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('heartbeat-nonce'),
			'checkIntervalMinutes' => 5,
			'warningTimeoutSeconds' => 10,
			'isKivicarePage' => true,
			'currentUrl' => $_SERVER['REQUEST_URI'] ?? '',
			'isAdmin' => is_admin()
		));
	}

	/**
	 * Check if current page is a Kivicare page
	 */
	private function isKivicarePage() {
		// Check if we're on the Kivicare dashboard
		if (is_admin() && isset($_GET['page']) && $_GET['page'] === 'dashboard') {
			return true;
		}

		// Check if we're on a page with Kivicare shortcodes
		global $post;
		if ($post && has_shortcode($post->post_content, 'kiviCare-patient-dashboard')) {
			return true;
		}
		if ($post && has_shortcode($post->post_content, 'kiviCare-book-appointment')) {
			return true;
		}
		if ($post && has_shortcode($post->post_content, 'kiviCare-patient-login')) {
			return true;
		}

		return false;
	}

	/**
	 * Disable WordPress auth check on Kivicare pages
	 */
	public function disableWpAuthCheck() {
		if ($this->isKivicarePage()) {
			return false;
		}
		return true;
	}

	/**
	 * Remove WordPress auth check HTML output
	 */
	public function removeAuthCheckHtml() {
		if ($this->isKivicarePage()) {
			// Remove any auth check related HTML that might have been added
			echo '<script type="text/javascript">
				// Remove WordPress auth check modal if it exists
				if (typeof wp !== "undefined" && wp.authcheck) {
					wp.authcheck.close();
				}
				// Remove auth check HTML elements
				jQuery(document).ready(function($) {
					$("#wp-auth-check-wrap, .wp-auth-check").remove();
				});
			</script>';
		}
	}

    function kc_remove_logo_wp_admin() {
        //remove WordPress logo from WordPress dashboard if user is plugin user
        $user_role = get_userdata(get_current_user_id());
        if(!empty($user_role->roles[0])){
            $user_role = $user_role->roles[0];
            if(in_array($user_role,[
                $this->getPatientRole(),
                $this->getDoctorRole(),
                $this->getReceptionistRole(),
                $this->getClinicAdminRole()
            ]) ){
                global $wp_admin_bar;
                $wp_admin_bar->remove_menu( 'wp-logo' );
            }
        }
    }

	public function adminMenu () {
        //add plugin menu in sidebar
		$site_title = get_bloginfo('name');
        $kivi_current_theme = get_stylesheet();
        if(!empty($kivi_current_theme) && $kivi_current_theme == 'kivicare'){
            add_menu_page( $site_title, 'Medroid' , kcGetPermission('dashboard'), 'dashboard/', [$this, 'adminDashboard'], $this->plugin_url . 'assets/images/sidebar-icon.svg', 4);
        }else{
            add_menu_page( $site_title, 'Medroid' , kcGetPermission('dashboard'), 'dashboard/', [$this, 'adminDashboard'], $this->plugin_url . 'assets/images/sidebar-icon.svg', 99);
        }
        $user_role = get_userdata(get_current_user_id());
        if(!empty($user_role->roles[0])){
            $user_role = $user_role->roles[0];
            if(in_array($user_role,[
                $this->getPatientRole(),
                $this->getDoctorRole(),
                $this->getReceptionistRole(),
                $this->getClinicAdminRole()
            ]) ){
                //remove WordPress notice and error message from dashboard
                remove_all_actions('admin_notices');
                remove_all_actions('all_admin_notices');
                remove_menu_page( 'index.php' );
            }
        }
	}
	public function adminDashboard() {
        // return dashboard page based on condition
        $langType = get_option(KIVI_CARE_PREFIX.'locoTranslateState');
		if(isKiviCareProActive() && ($langType != 1 || $langType != '1')) {
			$upload_dir = wp_upload_dir();
			$dir_name = KIVI_CARE_PREFIX.'lang';
			$user_dirname = $upload_dir['basedir'] . '/' . $dir_name;
			$current_lang_file = $user_dirname.'/temp.json';
			if(file_exists($current_lang_file) && filesize($current_lang_file) > 2000) {
				include(KIVI_CARE_DIR . 'resources/views/kc_dashboard.php');
			} else {
				include(KIVI_CARE_DIR . 'resources/views/kc_notice.php');
			}
		} else {
			include(KIVI_CARE_DIR . 'resources/views/kc_dashboard.php');
		}
	}
	public function enqueueStyles() {
        // Add cache busting with current time
        $cache_bust = time();
        //enqueue dashboard css
		// wp_enqueue_style( 'kc_google_fonts', $this->plugin_url . 'assets/css/poppins-google-fonts.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust );
		wp_enqueue_style( 'kc_google_fonts', $this->plugin_url . 'assets/css/custom-fonts.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust );
		// wp_enqueue_style( 'kc_app_min_style', $this->plugin_url . 'assets/css/app.min.css' , array(), KIVI_CARE_VERSION . '-' . $cache_bust );
		wp_enqueue_style( 'kc_app_min_style', $this->plugin_url . 'assets/css/tailwind.min.css' , array(), KIVI_CARE_VERSION . '-' . $cache_bust );
		wp_enqueue_style( 'kc_font_awesome', $this->plugin_url . 'assets/css/font-awesome-all.min.css' , array(), KIVI_CARE_VERSION . '-' . $cache_bust );
        wp_enqueue_style( 'kc_toastify',$this->plugin_url . 'assets/css/toastify.min.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );

        wp_dequeue_style( 'stylesheet' );
        wp_deregister_style('wp-admin');

    }
    public function loginPageStyles() {
        //enqueue wordpress login page style
	    // wp_enqueue_style( 'kc_app_min_style',  'http://localhost:8080/assets/css/app.min.css', array(), KIVI_CARE_VERSION  );
	    wp_enqueue_style( 'kc_app_min_style',  'http://localhost:8080/assets/css/tailwind.min.css', array(), KIVI_CARE_VERSION  );
    }

	public function enqueueScripts() {
        // Add cache busting with current time
        $cache_bust = time();

        // Condition for Development Enviroment Hot Reload
        if(file_exists(KIVI_CARE_DIR."hot")){
            $path= file_get_contents(KIVI_CARE_DIR."hot");
        }else{
            $path=$this->plugin_url;
        }

        //enqueue dashboard js
        wp_enqueue_script( 'jquery' );
        wp_enqueue_media();
        wp_add_inline_script( 'jquery-core', 'var wp = window.wp;' );
		wp_enqueue_script( 'kc_js_bundle', $path . 'assets/js/app.min.js', ['jquery','media-models'], KIVI_CARE_VERSION . '-' . $cache_bust, true);
        wp_enqueue_script( 'google-platform', 'https://apis.google.com/js/api.js', array(), KIVI_CARE_VERSION . '-' . $cache_bust, true );
        wp_enqueue_script( 'kc_custom', $path . 'assets/js/custom.js', ['jquery', 'media-models'], KIVI_CARE_VERSION . '-' . $cache_bust, true);
        wp_localize_script( 'kc_custom', 'kc_custom_request_data', [
                'support_mime_type' => get_allowed_mime_types()
        ]);
        //pass date to app.min.js file by localize
        wp_localize_script( 'kc_js_bundle', 'request_data', $this->getLocalizeScriptData('dashboard'));
		wp_enqueue_script( 'Js_bundle' );
        do_action('kivicare_enqueue_script','dashboard');

        wp_enqueue_script( 'kc_toastify',$this->plugin_url . 'assets/js/toastify-js.js' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, true );

	}
	public function enqueueFrontScripts() {
        wp_enqueue_script('jquery');
        $localizeArray = $this->getLocalizeScriptData('frontend');
        // Add cache busting with current time
        $cache_bust = time();
        //register frontend fontawesome css , enqueue when required
        wp_register_style( 'kc_font_awesome', $this->plugin_url . 'assets/css/font-awesome-all.min.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust ,false);

        wp_enqueue_style( 'kc_google_fonts', $this->plugin_url . 'assets/css/custom-fonts.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust );

        // Register and enqueue fullscreen login CSS and JS
        wp_register_style( 'kc_login_fullscreen', $this->plugin_url . 'assets/css/kivicare-login-fullscreen.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust );
        wp_register_script( 'kc_login_fullscreen_js', $this->plugin_url . 'assets/js/login-fullscreen.js', array(), KIVI_CARE_VERSION . '-' . $cache_bust, true );

        // Check if we're on one of our login pages and enqueue the fullscreen style
        global $post;
        if (is_object($post)) {
            $login_page_id = get_option('kivicare_register_login_page_id');
            $patient_login_page_id = get_option('kivicare_patient_login_page_id');

            if ($post->ID == $login_page_id || $post->ID == $patient_login_page_id) {
                // Enqueue our styles and scripts
                wp_enqueue_style('kc_login_fullscreen');
                wp_enqueue_script('kc_login_fullscreen_js');

                // Add special body class
                add_filter('body_class', function($classes) {
                    $classes[] = 'kivicare-fullscreen-page';
                    return $classes;
                });

                // Hide admin bar on login pages
                add_filter('show_admin_bar', '__return_false');

                // Output additional styles in the head
                add_action('wp_head', function() {
                    echo '<style>
                        html, body { margin-top: 0 !important; padding-top: 0 !important; }
                        body * { box-sizing: border-box !important; }
                        header, footer, nav, .site-header, .site-footer, #masthead, #colophon { display: none !important; }
                        #wpadminbar { display: none !important; }
                        .entry-content, .site-content, .content-area, #content {
                            min-height: 100vh !important;
                            width: 100% !important;
                            max-width: 100% !important;
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                    </style>';
                }, 999);

                // Remove all actions attached to wp_head, wp_footer hooks except our own
                global $wp_filter;
                if (isset($wp_filter['wp_head'])) {
                    $our_header_action = function() {};
                    add_action('wp_head', $our_header_action, 0);
                    foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
                        if ($priority !== 999) {
                            foreach ($callbacks as $callback => $config) {
                                remove_action('wp_head', $config['function'], $priority);
                            }
                        }
                    }
                }
            }
        }

        //register frontend vue shortcode css , enqueue when required
        // wp_register_style( 'kc_front_app_min_style', $this->plugin_url . 'assets/css/front-app.min.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust,false );
        wp_register_style( 'kc_front_app_min_style', $this->plugin_url . 'assets/css/tailwind.min.css', array(), KIVI_CARE_VERSION . '-' . $cache_bust,false );

        //register frontend vue shortcode js , enqueue when required
        wp_register_script( 'kc_front_js_bundle', $this->plugin_url . 'assets/js/front-app.min.js', ['jquery'], KIVI_CARE_VERSION . '-' . $cache_bust, true );
        //pass date to front-app.min.js file by localize
        wp_localize_script( 'kc_front_js_bundle', 'ajaxData', $localizeArray);
        wp_localize_script( 'kc_front_js_bundle', 'request_data', $localizeArray );

        //register custom js , enqueue when required
        wp_register_script( 'kc_custom', $this->plugin_url . 'assets/js/custom.js', ['jquery', 'media-models'], KIVI_CARE_VERSION . '-' . $cache_bust, true );

        //register axios js for ajax in shortcode, enqueue when required
        wp_register_script( 'kc_axios',$this->plugin_url . 'assets/js/axios.min.js' ,['jquery'], KIVI_CARE_VERSION . '-' . $cache_bust, true );

        //register flatpicker js and css for date select in appointment shortcode, enqueue when required
        wp_register_script( 'kc_flatpicker',$this->plugin_url . 'assets/js/flatpicker.min.js' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, true );
        wp_register_style( 'kc_flatpicker',$this->plugin_url . 'assets/css/flatpicker.min.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );

        wp_register_script( 'kc_toastify',$this->plugin_url . 'assets/js/toastify-js.js' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, true );
        wp_register_style( 'kc_toastify',$this->plugin_url . 'assets/css/toastify.min.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );

        //register new appointment shortcode main css, enqueue when required
        // wp_register_style( 'kc_book_appointment',$this->plugin_url . 'assets/css/book-appointment.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );
        wp_register_style( 'kc_appointment_booking_widget',$this->plugin_url . 'assets/css/appointment-booking-widget.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );

        //register css for login/register shortcode, enqueue when required
        // wp_register_style( 'kc_register_login',$this->plugin_url . 'assets/css/register_login.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );

        //register add-to-calender button js and css for new appointment shortcode, enqueue when required
        wp_register_script( 'kc_calendar',$this->plugin_url . 'assets/js/calendar.min.js' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, true );
        wp_register_style( 'kc_calendar',$this->plugin_url . 'assets/css/calendar.min.css' ,[], KIVI_CARE_VERSION . '-' . $cache_bust, false );

        //register print jquery js  for new appointment shortcode, enqueue when required
        wp_register_script( 'kc_print',$this->plugin_url . 'assets/js/jquery-print.min.js' ,['jquery'], KIVI_CARE_VERSION . '-' . $cache_bust, true );

        //register magnific popup js/css  for  appointment button shortcode, enqueue when required
        wp_register_style( 'kc_popup', $this->plugin_url . 'assets/css/magnific-popup.min.css', [], KIVI_CARE_VERSION . '-' . $cache_bust, false );
        wp_register_script( 'kc_popup', $this->plugin_url . 'assets/js/magnific-popup.min.js', ['jquery'], KIVI_CARE_VERSION . '-' . $cache_bust, true );
        do_action('kivicare_enqueue_script','shortcode_register');
    }

	public function appendToHeader () {
        // Add no-cache headers to prevent browser caching
        echo '<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />';
        echo '<meta http-equiv="Pragma" content="no-cache" />';
        echo '<meta http-equiv="Expires" content="0" />';

        //append required data in page meta
        $prefix = KIVI_CARE_PREFIX;
        $upload_dir = wp_upload_dir();
        $dir_name = $prefix .'lang';
        $user_dirname = $upload_dir['baseurl'] . '/' . $dir_name;
        $get_config =   get_option( KIVI_CARE_PREFIX . 'google_cal_setting',true);
        if(gettype($get_config) != 'boolean'){
            $client_id = $get_config['client_id'];
        }else{
            $client_id = '';
        }
        echo '<meta name="pluginBASEURL" content="' . esc_html($this->plugin_url) .'" />';
        echo '<meta name="pluginPREFIX" content="' . esc_html($this->getPluginPrefix()) .'" />';
        echo '<meta name="pluginMediaPath" content="' .esc_html($user_dirname) .'" />';
        echo '<meta name="google-signin-client_id" content="'.esc_html($client_id).'" />';

        //add root primary color ,if proactive
        $color = get_option(KIVI_CARE_PREFIX.'theme_color');
        if(!empty($color) && gettype($color) !== 'boolean' && isKiviCareProActive()){
            ?>
            <script> document.documentElement.style.setProperty("--primary", '<?php echo esc_js($color);?>');</script>
            <?php
        }
	}

	public function addAdministratorPermission () {
        //add permission to plugin user
		$admin_permissions = kcGetAdminPermissions()->pluck('name')->toArray();
		if (count($admin_permissions)) {
			$admin_role = get_role( 'administrator' );
			foreach ($admin_permissions as $permission) {
				$admin_role->add_cap( $permission, true );
			}
		}
	}
    public function migratePermissions(){

        //migrate permisssion to plugin user

        //migrate permisssion to plugin user
        if (!get_option(KIVI_CARE_PREFIX . 'permissions-migrate')) {

            remove_role($this->getClinicAdminRole());
            remove_role($this->getDoctorRole());
            remove_role($this->getPatientRole());
            remove_role($this->getReceptionistRole());

            $clinic_admin_permissions = kcGetAdminPermissions()->pluck('name')->toArray();
            $doctor_permissions = kcGetDoctorPermission()->pluck('name')->toArray();
            $patient_permissions = kcGetPatientPermissions()->pluck('name')->toArray();
            $receptionist_permissions = kcGetReceptionistPermission()->pluck('name')->toArray();

            // Assign permission to Clinic admin role...
            add_role($this->getClinicAdminRole(), 'Clinic admin', array_fill_keys($clinic_admin_permissions, 1));

            // Assign permission to Doctor role...
            add_role($this->getDoctorRole(), 'Doctor', array_fill_keys($doctor_permissions, 1));

            // Assign permission to Patient role...
            add_role($this->getPatientRole(), 'Patient', array_fill_keys($patient_permissions, 1));

            // Assign permission to Receptionist role...
            add_role($this->getReceptionistRole(), 'Receptionist', array_fill_keys($receptionist_permissions, 1));

            update_option(KIVI_CARE_PREFIX . 'permissions-migrate', 'yes');
        }

        $this->getClinicAdminRole();
        $this->getDoctorRole();
        $this->getPatientRole();
        $this->getReceptionistRole();

        return true;
    }
	public function hideSideBar() {
        //hide sidebar and notice from dashboard page
        remove_all_actions('admin_notices');
        remove_all_actions('all_admin_notices');
        echo '<style type="text/css">
					#wpcontent, #footer { margin-left: 0px !important;padding-left: 0px !important; }
					html.wp-toolbar { padding-top: 0px !important; }
					#adminmenuback, #adminmenuwrap, #wpadminbar, #wpfooter,#adminmenumain, #screen-meta { display: none !important; }
					#wpcontent .notice {
                     display:none;
                    }
				</style>';
	}
    public function kivicareShortcodeWidgetBlock() {

        global $pagenow;
        $script_dep = array( 'wp-blocks', 'wp-i18n', 'wp-element', 'wp-components');
        if ( 'widgets.php' !== $pagenow ) {
            $script_dep[] = 'wp-editor';
        }

        // Add cache busting with current time
        $cache_bust = time();

        //vue appointment book shortcode script enqueue
        wp_enqueue_script(
            'kivi-care-appointment-widget',
            $this->plugin_url . 'assets/js/KC-appointment-block.js',
            $script_dep, KIVI_CARE_VERSION . '-' . $cache_bust, true
        );

        //vue patient dashboard shortcode script enqueue
        wp_enqueue_script(
            'kivi-care-patient-dashboard-widget',
            $this->plugin_url . 'assets/js/kc-patient-dashboard-block.js',
            $script_dep,
            KIVI_CARE_VERSION . '-' . $cache_bust, true
        );

        //new appointment book shortcode script enqueue
        wp_enqueue_script(
            'kivi-care-book-appointment-widget',
            $this->plugin_url . 'assets/js/KC-book-appointment-block.js',
            $script_dep, KIVI_CARE_VERSION . '-' . $cache_bust, true
        );

        //new popup/button appointment book shortcode script enqueue
        wp_enqueue_script(
            'kivi-care-popup-book-appointment-widget',
            $this->plugin_url . 'assets/js/KC-popup-book-appointment-block.js',
            $script_dep,
            KIVI_CARE_VERSION . '-' . $cache_bust, true
        );

        //login/register shortcode script enqueue
        wp_enqueue_script(
            'kivi-care-register-login-widget',
            $this->plugin_url . 'assets/js/kc-register-login-block.js',
            $script_dep,
            KIVI_CARE_VERSION . '-' . $cache_bust, true
        );

		global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();

        $clinicsmappingForShortCode = [];
        if(isKiviCareProActive()){
            //doctors clinic list
            $clinicsmappingForShortCode = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}kc_doctor_clinic_mappings ", ARRAY_A);
            //all clinic list
            $clinicsListForShortCode = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}kc_clinics", ARRAY_A);
        }else{
            //get default clinic detail
            $clinicsListForShortCode = kcGetDefaultClinic();
        }

        //all doctors list who status is active
        $doctorsListForShortCode = get_users([
            'role' => $this->getDoctorRole(),
            'user_status' => '0'
        ]);
        $doctorsListForShortCode = (array) $doctorsListForShortCode;

        //pass data to widget js file
        wp_localize_script( 'kivi-care-appointment-widget', 'clincData', array(
            'clinics' => $clinicsListForShortCode,
            'doctors' => $doctorsListForShortCode,
            'mappingData' => $clinicsmappingForShortCode,
            'proActive' => isKiviCareProActive()
        ));

        //pass data to widget js file
        wp_localize_script( 'kivi-care-book-appointment-widget', 'clincDataNew', array(
            'clinics' => $clinicsListForShortCode,
            'doctors' => $doctorsListForShortCode,
            'mappingData' => $clinicsmappingForShortCode,
            'proActive' => isKiviCareProActive()
        ));

    }

    public function addBlockCategories( $categories ) {

        //add plugin widget in gutenberg
        return array_merge( array(
            array(
                'slug'  => 'kivi-appointment-widget',
                'title' => 'KiVi Care',
            ), ),
            $categories
        );
    }
	public function redirectUserToDashboard( $redirect_to, $request, $user ) {

        //redirect plugin user to dashboard
		if ( isset( $user->roles ) && is_array( $user->roles ) ) {
			// check for other user roles...
			if (in_array( $this->getClinicAdminRole(), $user->roles ) ) {
                return kcGetLogoinRedirectSetting('clinic_admin');
			} elseif (in_array( $this->getReceptionistRole(), $user->roles )) {
                return kcGetLogoinRedirectSetting('receptionist');
			} elseif (in_array( $this->getDoctorRole(), $user->roles )) {
                return kcGetLogoinRedirectSetting('doctor');
			} elseif (in_array( $this->getPatientRole(), $user->roles )) {
                return kcGetLogoinRedirectSetting('patient');
			}
		}

		return $redirect_to;
	}
	public static function migrateDatabase () {
        //include all databases file
        $databases = glob(KIVI_CARE_DIR . 'app/database/*.php');
        require_once ABSPATH . '/wp-admin/includes/upgrade.php';
        foreach ($databases as $key => $file){
            if(file_exists($file)){
                require $file;
            }
        }

        // Create or update SignatureRX table
        if(file_exists(KIVI_CARE_DIR . 'app/database/kc-signaturerx-db.php')) {
            require_once KIVI_CARE_DIR . 'app/database/kc-signaturerx-db.php';
            kcCreateSignatureRXTable();

            // Update SignatureRX table with additional fields for commission tracking
            global $wpdb;
            $table_name = $wpdb->prefix . 'kc_signaturerx';

            $new_fields = [
                'clinic_id' => 'bigint(20)',
                'prescription_action' => 'varchar(50)',
                'aff_tag' => 'varchar(255)',
                'request_data' => 'text',
                'updated_at' => 'datetime'
            ];

            // Add new columns to the SignatureRX table if they don't exist
            kcUpdateFields($table_name, $new_fields);
        }
	}

	// Manual function to create SignatureRX table - can be called directly
	public static function createSignatureRXTable() {
	    require_once ABSPATH . '/wp-admin/includes/upgrade.php';
	    require_once KIVI_CARE_DIR . 'app/database/kc-signaturerx-db.php';
	    kcCreateSignatureRXTable();
	}
	public function addDefaultOptions () {

        //add all default option when plugin activate
        // Add default SignatureRX options
        if (!get_option('kc_signaturerx_client_id')) {
            update_option('kc_signaturerx_client_id', '39');
        }
        if (!get_option('kc_signaturerx_client_secret')) {
            update_option('kc_signaturerx_client_secret', 'fvBoK4QH4oQeRoxR5AWWNToeEMCBN5k48vTbFVlw');
        }

        $steps = $this->getSetupSteps();
		$moduleSetting = [
            $steps => 4,
			'common_setting' => [
				'patient_reminder' =>
					[
						'label' => 'Patient appointment reminder switch',
						'status' => 1
					]
			]
		];

		foreach ($moduleSetting as $key => $value) {
			add_option($key , $value);
		}

		$setup_config_name = KIVI_CARE_PREFIX . 'setup_config';
		add_option($setup_config_name, json_encode(kcGetSetupWizardOptions()));

        //Add Clinic Setup for clinic user
        if (!get_option( 'add_clinic_setup_done_already_exist_clinic')) {

            $defaultSetupWizardOption= kcGetSetupWizardOptions(true);
            foreach (get_users([
                'role' => $this->getClinicAdminRole(),
                'fields' => 'ID'
            ]) as $user ) {
              update_user_meta($user,$setup_config_name, json_encode(kcGetSetupWizardOptions()));
            };
            add_option('add_clinic_setup_done_already_exist_clinic', 1);
        }

		if (!get_option( 'is_kivicarepro_upgrade_lang')) {
			add_option('is_kivicarepro_upgrade_lang', 1);
			$this->updateLangFile();
		}

		if (!get_option( 'is_lang_version_2')) {
			add_option('is_lang_version_2', 1);
			$this->getLangCopy();
			$this->mergeJson();
		}

		if(!get_option( KIVI_CARE_PREFIX . 'woocommerce_payment' )) {
			update_option( KIVI_CARE_PREFIX . 'woocommerce_payment', 'off', 'no' );
		}

        if(!get_option( KIVI_CARE_PREFIX . 'country_code' )) {
			update_option(KIVI_CARE_PREFIX . 'country_code', 'GB');
		}

        if(!get_option( KIVI_CARE_PREFIX . 'country_calling_code' )) {
			update_option(KIVI_CARE_PREFIX . 'country_calling_code', '44');
		}

        $user_role = [
            'kiviCare_patient' => 'on',
            'kiviCare_doctor' => 'on',
            'kiviCare_receptionist' => 'on'
        ];

        if(!get_option( KIVI_CARE_PREFIX . 'user_registration_shortcode_role_setting' )) {
            update_option(KIVI_CARE_PREFIX . 'user_registration_shortcode_role_setting', $user_role);
		}

        if(!get_option( KIVI_CARE_PREFIX . 'appointment_cancellation_buffer' )) {
            update_option(KIVI_CARE_PREFIX . 'appointment_cancellation_buffer',[
                "status" => 'off',
                "time" => '']
            );
		}

	}
	public function addDefaultModuleConfig() {

        //add default option for module configuration
		$prefix = $this->getPluginPrefix();

		$modules = [
			'module_config' => [
				[
					'name' => 'receptionist',
					'label' => 'Receptionist',
					'status' => '1'
				],
				[
					'name' => 'billing',
					'label' => 'Billing',
					'status' => '1'
				],
				[
					'name' => 'custom_fields',
					'label' => 'Custom Fields',
					'status' => '1'
				]
			],
			'common_setting' => [],
			'notification' => []
		];

		delete_option($prefix.'modules');
		add_option( $prefix.'modules', json_encode($modules));

	}
	public function versionUpgradePatches () {
        //not use function
		require KIVI_CARE_DIR . 'app/upgrade/kc-default-value-upgrade.php';
    }
	public function kivicare_agent_admin_access( $prevent_access ) {
        //restrict woocommerce user access
        if( current_user_can('read') ) $prevent_access = false;
        return $prevent_access;
    }
	public function mergeJson(){

        //new language key add to old language file (deprecated language )
		//upload dir
		$upload_dir = wp_upload_dir();
		$dir_name = KIVI_CARE_PREFIX.'lang';
		$user_dirname = $upload_dir['basedir'] . '/' . $dir_name;

		//get latest key from the en.json
		$newEn = KIVI_CARE_DIR_URI.'resources/assets/lang/en.json';
		$enContent =  file_get_contents($newEn);

		//get all lang of user from the database.
		$get_user_lang = get_option(KIVI_CARE_PREFIX.'lang_option');
		$data = json_decode($get_user_lang,true);

		//store merge data.
		$output = [];

		//merge new en content in all database file.
		foreach ($data['lang_option'] as $key => $value) {

			//get all file based on databse lang value.
			$all_database_file  = $user_dirname.'/'.$value['id'].'.json';
            if(file_exists($all_database_file)){
                $all_file_content = file_get_contents($all_database_file);

                // get file value in multidimention array.
                $value_arry = json_decode($all_file_content, true);

                //merge new value in all database file.
                $temp_new = json_decode($enContent,true);
                if(!empty($temp_new) && is_array($temp_new)){

                    foreach (json_decode($enContent,true) as $key => $lang) {
                        $output[$key] = array_merge($lang,!empty($value_arry[$key]) ? $value_arry[$key] : []);
                    }

                    //put all new keys in all file.
                    file_put_contents($user_dirname.'/'.$value['id'].'.json', json_encode($output));
                }
            }

		}

		//get temp file
		$current_lang_file = $user_dirname.'/temp.json';
		if(file_exists($current_lang_file)){
			//put all temp content in below array.
			$temp_output = [];

			//get temp file value
			$get_current_lang_content = file_get_contents($current_lang_file);

			foreach (json_decode($enContent,true) as $key => $lang) {

				// merge all key in temp
				$temp_output[$key] = array_merge($lang,!empty($get_current_lang_content[$key]) ? $get_current_lang_content[$key] : []);
			}

			//put all key in temp file.
			file_put_contents($current_lang_file, json_encode($temp_output));
		}else{
            $directory = dirname($current_lang_file); // Extract the directory from the file path.

            if (!is_dir($directory)) {
                // The directory doesn't exist, so create it.
                mkdir($directory, 0755);
            }

            // Now, you can proceed with writing the file.
            file_put_contents($current_lang_file, json_encode($enContent));
		}
	}
	public function getLangCopy(){

        //copy language folder as a backup (deprecated language)
		$upload_dir = wp_upload_dir();
		$backup_dir_name = $upload_dir['basedir'].'/'.KIVI_CARE_PREFIX.'backup';
		$backup_folder =  $upload_dir['basedir'].'/'.KIVI_CARE_PREFIX.'backup/'.current_time('Y-m-d') .'_lang';

		$dir_name = KIVI_CARE_PREFIX.'lang';
		$user_dirname = $upload_dir['basedir'] . '/' . $dir_name;

		if(!file_exists($backup_dir_name)) {
			wp_mkdir_p( $backup_dir_name );
		}
		if(!file_exists($backup_folder)){
			wp_mkdir_p( $backup_folder );
			$get_user_lang = get_option(KIVI_CARE_PREFIX.'lang_option');
			$data = json_decode($get_user_lang,true);
			foreach ($data['lang_option'] as $key => $value) {
				$old_file  = $user_dirname.'/'.$value['id'].'.json';
				$new = $backup_folder.'/'.$value['id'].'.json';
                if (file_exists($old_file)) {
                    $data = file_get_contents($old_file);
                    if ($data !== false) {
                        file_put_contents($new,$data);
                        chmod($new, 0777);
                    }
                }
			}
			$old_temp  = $user_dirname.'/temp.json';
			$new_temp = $backup_folder.'/temp.json';
            if (file_exists($old_temp)) {
                $temp_data = file_get_contents($old_temp);
                if ($temp_data !== false) {
                    file_put_contents($new_temp,$temp_data);
                    chmod($new_temp, 0777);
                }
            }
		}
	}

   public function kcEnableLocoTranslate(){

        //redirect when user change deprecate language translate to loco translate for notice page
       if(isset($_GET['kcEnableLocoTranslation'])) {
           update_option(KIVI_CARE_PREFIX.'locoTranslateState', 1);
           wp_redirect(admin_url( 'admin.php?page=dashboard' ));
       }

       //redirect after appointment payment by PayPal gateway
       if(isset($_GET['kivicare_payment'])){
           if($_GET['kivicare_payment'] === 'failed'){
               if($this->getLoginUserRole() === $this->getPatientRole()){
	               (new KCPaymentController())->paymentFailedPage();
	               //if payment failed
               }
           }elseif ($_GET['kivicare_payment'] === 'success'){
	           if($this->getLoginUserRole() === $this->getPatientRole()){
		           //if payment success
		           (new KCPaymentController())->paymentSuccess();
	           }
           }
           die;
       }
   }

   public function addCustomPostType(){
       /**
        * function to register custom post type
        */
       register_post_type(KIVI_CARE_PREFIX.'sms_tmp',
           array(
               'labels' => array(
                   'name' => 'KivicareSms',
                   'singular_name' => 'kivicaresms'
               ),
               'public' => true,
               'has_archive' => 'false',
               'rewrite' => array('slug' => 'kivicaresms'),
               'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'author'),
               'description' => esc_html__('Custom kivicaresms Posts','kc-lang'),
               'show_ui' => false,
               'show_in_menu' => false,
               'map_meta_cap' => true,
               'capability_type'     => 'post',
           )
       );

       register_post_type(KIVI_CARE_PREFIX.'mail_tmp',
           array(
               'labels' => array(
                   'name' => 'KivicareMail',
                   'singular_name' => 'kivicaremail'
               ),
               'public' => true,
               'has_archive' => 'false',
               'rewrite' => array('slug' => 'kivicaremail'),
               'supports' => array('title', 'editor', 'thumbnail', 'excerpt','author'),
               'description' => esc_html__('Custom kivicaremail Posts','kc-lang'),
               'show_ui' => false,
               'show_in_menu' => false,
               'map_meta_cap' => true,
               'capability_type' => 'post',

           )
       );

       register_post_type(KIVI_CARE_PREFIX.'gcal_tmp',
           array(
               'labels' => array(
                   'name' => 'KivicareGoogleEvent',
                   'singular_name' => 'KivicareGoogleEvent'
               ),
               'public' => true,
               'has_archive' => 'false',
               'rewrite' => array('slug' => 'Kivicaregoogleevent'),
               'supports' => array('title', 'editor', 'thumbnail', 'excerpt','author'),
               'description' => esc_html__('Custom kivicare Google Event Posts','kc-lang'),
               'show_ui' => false,
               'show_in_menu' => false,
               'map_meta_cap' => true,
               'capability_type' => 'post',

           )
       );

       register_post_type(KIVI_CARE_PREFIX.'gmeet_tmp',
           array(
               'labels' => array(
                   'name' => 'KivicareGoogleMeetEvent',
                   'singular_name' => 'KivicareGoogleMeetEvent'
               ),
               'public' => true,
               'has_archive' => 'false',
               'rewrite' => array('slug' => 'Kivicaregooglemeetevent'),
               'supports' => array('title', 'editor', 'thumbnail', 'excerpt','author'),
               'description' => esc_html__('Custom kivicare Google Meet Event Posts','kc-lang'),
               'show_ui' => false,
               'show_in_menu' => false,
               'map_meta_cap' => true,
               'capability_type' => 'post',

           )
       );

       if ( !get_option(KIVI_CARE_PREFIX.'doctor_appointment_remainder_template_added')){
            update_option(KIVI_CARE_PREFIX.'doctor_appointment_remainder_template_added','yes');
            $this->addDoctorEmailTemplate();
       }

       if(!get_option( KIVI_CARE_PREFIX.'notification_template_added')){
           update_option(KIVI_CARE_PREFIX.'notification_template_added','yes');
           $this->addDefaultPosts();
        }
        if (!get_option(KIVI_CARE_PREFIX.'send_patient_payment_link')){
            update_option(KIVI_CARE_PREFIX.'send_patient_payment_link','yes');
            $this->sendPatientPaymentLinkTemplate();
        }
        if (!get_option(KIVI_CARE_PREFIX.'send_patient_bill_payment_link')){
            update_option(KIVI_CARE_PREFIX.'send_patient_bill_payment_link','yes');
            $this->sendPatientBillPaymentLinkTemplate();
        }

        if (!get_option(KIVI_CARE_PREFIX.'send_patient_invoice')){
            update_option(KIVI_CARE_PREFIX.'send_patient_invoice','yes');
            $this->addPatientInvoiceTemplate();
        }


    }

   public function addDoctorEmailTemplate () {
        //doctor appointment remainder template
        $template = [
            [
                'post_name' => KIVI_CARE_PREFIX . 'book_appointment_reminder_for_doctor',
                'post_content' => '<p> Welcome to KiviCare ,</p><p> You Have appointment  on </p><p> {{appointment_date}}  , Time : {{appointment_time}}  , Patient : {{patient_name}} </p><p> Thank you. </p>',
                'post_title' => 'Doctor Appointment Reminder',
                'post_type' => KIVI_CARE_PREFIX.'mail_tmp',
                'post_status' => 'publish',
            ],
            [
                'post_name' => KIVI_CARE_PREFIX . 'book_appointment_reminder_for_doctor',
                'post_content' => '<p> Welcome to KiviCare ,</p><p> You Have appointment  on </p><p> {{appointment_date}}  , Time : {{appointment_time}}  , Patient : {{patient_name}} </p><p> Thank you. </p>',
                'post_title' => 'Doctor Appointment Reminder',
                'post_type' => KIVI_CARE_PREFIX.'sms_tmp',
                'post_status' => 'publish',
            ],
        ];
        kcAddMailSmsPosts($template);
   }
   public function sendPatientPaymentLinkTemplate () {
        $template = [
            [
                'post_name' => KIVI_CARE_PREFIX . 'patient_payment_link',
                'post_content' => '<p>Hello {{patient_name}},</p><p>Please complete your payment for the appointment scheduled on {{appointment_date}} at {{appointment_time}}.</p><p>Payment Link: <a href="{{payment_link}}">Click here to pay</a></p><p>Amount Due: {{payment_amount}}</p><p>This payment link will expire in 15 Minutes.</p><p>Thank you for choosing Medroid.</p>',
                'post_title' => 'Patient Payment Link',
                'post_type' => KIVI_CARE_PREFIX.'mail_tmp',
                'post_status' => 'publish',
            ]
        ];
        kcAddMailSmsPosts($template);
    }
    public function sendPatientBillPaymentLinkTemplate() {
        $template = [
            [
                'post_name' => KIVI_CARE_PREFIX . 'patient_bill_payment_link',
                'post_content' => '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Consultation Payment</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .email-header {
            background-color: #0d6efd;
            padding: 20px;
            text-align: center;
        }
        .email-header h1 {
            color: white;
            margin: 0;
            font-size: 24px;
        }
        .email-content {
            padding: 30px;
        }
        .email-footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .btn-payment {
            padding: 10px 25px;
            font-weight: 500;
        }
        /* Fix for email clients */
        table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
        }
        table td, table th {
            border: 1px solid #dee2e6;
            padding: 8px;
        }
        table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
        .total-row {
            font-weight: bold;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1 class="fw-bold">Thank You for Your Consultation</h1>
        </div>

        <div class="email-content">
            <p>Dear <strong>[Patient Name]</strong>,</p>

            <p>Thank you for consulting with Dr. <strong>[Doctor Name]</strong> on <strong>[Consultation Date]</strong>. We hope you found the session helpful and informative.</p>

            <p class="mb-2">Below is a summary of the services provided during your consultation:</p>

            <table class="mb-4">
                <thead>
                    <tr>
                        <th>Service Description</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>[Service 1]</td>
                        <td>$[Amount 1]</td>
                    </tr>
                    <tr>
                        <td>[Service 2]</td>
                        <td>$[Amount 2]</td>
                    </tr>
                    <tr class="total-row">
                        <td>Total</td>
                        <td>$[Total Amount]</td>
                    </tr>
                </tbody>
            </table>

            <p class="mb-3">Please click the button below to complete your payment:</p>

            <div class="text-center mb-4">
                <a href="[Payment Link URL]" class="btn btn-success btn-payment">Pay Now</a>
            </div>

            <div class="alert alert-info p-3 mb-4" role="alert">
                <small><strong>Note:</strong> This payment link will expire in <strong>48 hours</strong>.</small>
            </div>

            <p>If you have any questions about your bill or need assistance, please contact our billing department at <a href="mailto:<EMAIL>"><EMAIL></a> or call us at (*************.</p>

            <p class="mb-0">For your records, your consultation reference number is: <strong>#[Reference Number]</strong></p>
        </div>

        <div class="email-footer">
            <p class="mb-2"><strong>[Clinic/Hospital Name]</strong><br>
            [Address Line 1]<br>
            [Address Line 2]<br>
            Phone: (*************<br>
            Email: <EMAIL></p>

            <p class="mb-0 text-muted"><small>This email contains confidential information and is intended only for the named recipient. If you have received this email in error, please notify the sender immediately.</small></p>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper (optional, but might be needed for some interactive elements) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>',
                'post_title' => 'Patient Payment Link',
                'post_type' => KIVI_CARE_PREFIX.'mail_tmp',
                'post_status' => 'publish',
            ]
        ];
        kcAddMailSmsPosts($template);
    }
    public function sendPatientInvoice()  {

        $template = [
            [
                'post_name' => KIVI_CARE_PREFIX . 'payment_invoice',
                'post_content' => include_once KIVI_CARE_DIR . 'templates/emails/PatientInvoice.php',
                'post_title' => 'Patient Invoice',
                'post_type' => KIVI_CARE_PREFIX.'mail_tmp',
                'post_status' => 'publish',
            ]
        ];
        kcAddMailSmsPosts($template);
    }

    public function addPatientInvoiceTemplate () {
        //google event template
        $commonTemplate = [
            [
                'post_name' => KIVI_CARE_PREFIX . 'patient_invoice',
                'post_content' => include_once KIVI_CARE_DIR . 'templates/emails/PatientInvoice.php',
                'post_title' => 'Patient Invoice',
                'post_type' => KIVI_CARE_PREFIX.'mail_tmp',
                'post_status' => 'publish'
            ],
        ];
        kcAddMailSmsPosts($commonTemplate);
    }

    public function addDefaultPosts () {

        //email template
        kcAddMailSmsPosts(kcCommonTemplate('mail'));

        //sms template
        kcAddMailSmsPosts(kcCommonTemplate('sms'));

        //google event template
        $commonTemplate = [
            [
                'post_title' => '{{service_name}}',
                'post_content'  =>' Appointment booked at {{clinic_name}}',
                'post_status'   => 'publish',
                'post_type' => KIVI_CARE_PREFIX.'gcal_tmp',
                'post_name' => KIVI_CARE_PREFIX.'default_event_template',
            ]
        ];
        kcAddMailSmsPosts($commonTemplate);

        //googlemeet event template
        $commonTemplate = [array(
            'post_title' => '{{service_name}}',
            'post_content'  => '<p> New appointment </p><p> Your have new appointment on </p><p> Date: {{appointment_date}}  , Time : {{appointment_time}} ,Patient : {{patient_name}} </p><p> Clinic: {{clinic_name}}. </p><p> Appointment Description: {{appointment_desc}}. </p><p> Thank you. </p>',
            'post_status'   => 'publish',
            'post_type' => KIVI_CARE_PREFIX.'gmeet_tmp',
            'post_name' => KIVI_CARE_PREFIX.'doctor_gm_event_template',
        )];

        kcAddMailSmsPosts($commonTemplate);
    }

   public function appendLanguageInHead(){
       //browser tab icon change (use for plugin sandbox)
       if(gettype(get_option('mp_demo_sandbox_id',true)) !== 'boolean' && is_multisite()){
           $iconUrl = KIVI_CARE_DIR_URI.'assets/images/favicon-1.ico';
           ?>
           <link rel="shortcut icon" href="<?php echo esc_url($iconUrl);?>"/>
           <link rel="apple-touch-icon" href="<?php echo esc_url($iconUrl);?>">
           <?php
       }

       //append translated language object to page by global window variable
       kcAppendLanguageInHead();
   }

    public function kccheckActiveUser($user, $password){
        $user_data = $user->data;
        $user_id = $user_data->ID;
        $user_role = get_userdata($user_id);
        if(empty($user_role->roles[0])){
            return $user;
        }
        $user_role = $user_role->roles[0];
        //check plugin login user status ,if status is not active return error response
        if(in_array($user_role,[
            $this->getPatientRole(),
            $this->getDoctorRole(),
            $this->getReceptionistRole(),
            $this->getClinicAdminRole()
        ]) ){
            global $wpdb;
            $user_status = $wpdb->get_var("SELECT user_status FROM {$wpdb->base_prefix}users WHERE ID = {$user_id}");
            if( $user_status == 1 ){
                return new WP_Error('disabled_account', __('This User account is Not Activated','kc-lang'));
            }
        }
        return  $user;
    }

    public function getLocalizeScriptData($type){
        $prefix = KIVI_CARE_PREFIX;
        $config_options = kc_get_multiple_option("
            '{$prefix}appointment_description_config_data',
            '{$prefix}appointment_patient_info_config_data',
            '{$prefix}copyrightText',
            '{$prefix}site_logo',
            '{$prefix}request_helper_status',
            '{$prefix}theme_color',
            'mp_demo_sandbox_id'
        ");
        //localize data
        $lang = explode('_',get_locale());
        $lang = !empty($lang[0]) ? $lang[0] : 'en';
        $enableAppointmentDescription = !empty($config_options[KIVI_CARE_PREFIX.'appointment_description_config_data']) ? $config_options[KIVI_CARE_PREFIX.'appointment_description_config_data'] : 'off';
        $enablePatientInfo = !empty($config_options[KIVI_CARE_PREFIX.'appointment_patient_info_config_data']) ? $config_options[KIVI_CARE_PREFIX.'appointment_patient_info_config_data'] : 'off';
        $data = [
            'ajaxurl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce('ajax_post'),
            'get_nonce' => wp_create_nonce('ajax_get'),
            'kiviCarePluginURL' => $this->plugin_url,
            'loaderImage' =>  kcPluginLoader(),
            'homePage' => get_home_url(),
            // 'appointment_time_format' => kcGetAppointmentTimeFormatOption(),
            'current_user_role' => $this->getLoginUserRole(),
            'current_wordpress_lang' => $lang,
            'proActive' => isKiviCareProActive(),
            'appointment_restrict' => kcAppointmentRestrictionData(),
            'date_format' => kcGetDateFormat(),
            'logout_redirect_url' =>kcGetLogoutRedirectSetting('all'),
            'copyrightText' => !empty($config_options[KIVI_CARE_PREFIX.'copyrightText']) ? $config_options[KIVI_CARE_PREFIX.'copyrightText'] : '',
            'file_upload_status' => kcAppointmentMultiFileUploadEnable() ? 'on' : 'off',
            'description_status' => $enableAppointmentDescription,
            'patient_detail_info_status' => $enablePatientInfo,
            'menu_url' => admin_url('admin.php?page=dashboard')
        ];

        if($type === 'frontend'){
            $site_logo  = !empty($config_options[KIVI_CARE_PREFIX.'site_logo'])
                ? wp_get_attachment_url($config_options[KIVI_CARE_PREFIX.'site_logo']) : $this->plugin_url.'assets/images/logo-banner.png';
            $temp = [
                'site_logo' => $site_logo,
                'forget_password_page' => wp_lostpassword_url(),
                'default_clinic_id'   => kcGetDefaultClinicId(),
                'page_id' => get_the_ID(  )
            ];

            $data = array_merge($data,$temp);
        }

        if($type === 'dashboard'){
            $temp = [
                'new_user' => kcToCheckUserIsNew() ? 'true' : 'false',
                'adminUrl' => admin_url(),
                "color" => !empty($config_options[KIVI_CARE_PREFIX.'theme_color']) ? $config_options[KIVI_CARE_PREFIX.'theme_color']: '#000000',
                'link_show_hide' => !empty($config_options[KIVI_CARE_PREFIX.'request_helper_status']) && $config_options[KIVI_CARE_PREFIX.'request_helper_status'] == 'on' ? 'on' : 'off',
                'understand_loco_translate' => kcGetiUnderstand(),
                'time_zone_data' => kcGetTimeZoneOption(),
                'allClinicHaveSession' => kcGetAllClinicHaveSession(),
                'wordpress_logo' => kcWordpressLogostatusAndImage('image'),
                'demo_plugin_active' => !empty($config_options['mp_demo_sandbox_id']) && is_multisite(),
                'wordpress_logo_status' => kcWordpressLogostatusAndImage('status')
            ];
            $data = array_merge($data,$temp);
        }
        return apply_filters( 'kivicare_localize_request_data', $data, $type );
    }

    public function kcElementerCategoryRegistered($elements_manager){
        $elements_manager->add_category(
            'kivicare-widget-category',
            [
                'title' => 'Kivicare',
                'icon' => 'fas fa-clinic-medical',
            ]
        );
    }

    public function kcAddElementorWidget(){
        require_once(KIVI_CARE_DIR . 'app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php' );
        require_once(KIVI_CARE_DIR . 'app/baseClasses/KCElementor/KCElementorClinicCard.php' );
    }

    public function kcChangeWordpressLogo() {
        ?>
        <style type="text/css">
            body.login div#login h1 a {
                background-image: url(<?php echo esc_url(kcWordpressLogostatusAndImage('image')); ?>);
                padding-bottom: 30px;
            }
        </style>
        <?php
    }

    public static function createShortcodePage(){
        $pages = apply_filters(
            'kivicare_create_pages',
            array(
                'appointment'           => array(
                    'name'    => _x( 'appointment', 'Page slug', 'kc-lang' ),
                    'title'   => _x( 'Appointment', 'Page title', 'kc-lang' ),
                    'content' => '<!-- wp:shortcode -->[kivicareBookAppointment]<!-- /wp:shortcode -->',
                    'search_value' => '[kivicareBookAppointment'
                ),
                'patient_dashboard'       => array(
                    'name'    => _x( 'patient-dashboard', 'Page slug', 'kc-lang' ),
                    'title'   => _x( 'Patient Dashboard', 'Page title', 'kc-lang' ),
                    'content' => '<!-- wp:shortcode -->[patientDashboard]<!-- /wp:shortcode -->',
                    'search_value' => '[patientDashboard]'
                ),
                'register_login'       => array(
                    'name'    => _x( 'register-login', 'Page slug', 'kc-lang' ),
                    'title'   => _x( 'Register Login user', 'Page title', 'kc-lang' ),
                    'content' => '<!-- wp:shortcode -->[kivicareRegisterLogin]<!-- /wp:shortcode -->',
                    'search_value' => '[kivicareRegisterLogin]'
                ),
                'patient_login'       => array(
                    'name'    => _x( 'patient-login', 'Page slug', 'kc-lang' ),
                    'title'   => _x( 'Patient Login', 'Page title', 'kc-lang' ),
                    'content' => '<!-- wp:shortcode -->[medroidPatientLogin]<!-- /wp:shortcode -->',
                    'search_value' => '[medroidPatientLogin]'
                ),
            )
        );

        foreach ( $pages as $key => $page ) {
            self::createPage(
                esc_sql( $page['name'] ),
                'kivicare_' . $key . '_page_id',
                $page['title'],
                $page['content'],
                ! empty( $page['post_status'] ) ? $page['post_status'] : 'publish',
                $page['search_value']
            );
        }
    }

    public static function createPage( $slug, $option = '', $page_title = '', $page_content = '', $post_status = 'publish',$searchValue ='' ) {
        global $wpdb;

        $option_value = get_option( $option );

        if ( $option_value > 0 ) {
            $page_object = get_post( $option_value );
            if ( $page_object && 'page' === $page_object->post_type && ! in_array( $page_object->post_status, array( 'pending', 'trash', 'future', 'auto-draft' ), true ) ) {
                // Valid page is already in place.
                return;
            }
        }

        if ( strlen( $searchValue ) > 0 ) {
            // Search for an existing page with the specified page content (typically a shortcode).
            $valid_page_found = $wpdb->get_var( $wpdb->prepare( "SELECT ID FROM {$wpdb->posts} WHERE post_type='page' AND post_status NOT IN ( 'pending', 'trash', 'future', 'auto-draft' ) AND post_content LIKE %s LIMIT 1;", "%{$searchValue}%" ) );
            if(!empty($valid_page_found)){
                update_option( $option, $valid_page_found );
                return ;
            }
        }


        // Search for a matching valid trashed page.
        if ( strlen( $searchValue ) > 0 ) {
            // Search for an existing page with the specified page content (typically a shortcode).
            $trashed_page_found = $wpdb->get_var( $wpdb->prepare( "SELECT ID FROM $wpdb->posts WHERE post_type='page' AND post_status = 'trash' AND post_content LIKE %s LIMIT 1;", "%{$searchValue}%" ) );
        } else {
            // Search for an existing page with the specified page slug.
            $trashed_page_found = $wpdb->get_var( $wpdb->prepare( "SELECT ID FROM $wpdb->posts WHERE post_type='page' AND post_status = 'trash' AND post_name = %s LIMIT 1;", $slug ) );
        }

        if ( !empty($trashed_page_found) ) {
            $page_id   = $trashed_page_found;
            $page_data = array(
                'ID'          => $page_id,
                'post_status' => $post_status,
            );
            wp_update_post( $page_data );
        } else {
            $page_data = array(
                'post_status'    => $post_status,
                'post_type'      => 'page',
                'post_author'    => get_current_user_id(),
                'post_name'      => $slug,
                'post_title'     => $page_title,
                'post_content'   => $page_content,
                'comment_status' => 'closed',
            );
            $page_id   = wp_insert_post( $page_data );
        }

        update_option( $option, $page_id );

        return;
    }

    public function add_display_post_states( $post_states, $post ) {

        if ( $this->kc_get_page_id( 'appointment' ) === $post->ID ) {
            $post_states['appointment'] = __( 'Appointment Booking Page', 'kc-lang' );
        }

        if (  $this->kc_get_page_id( 'patient_dashboard' ) === $post->ID ) {
            $post_states['patient_dashboard'] = __( 'Patient Dashboard Page', 'kc-lang' );
        }

        if (  $this->kc_get_page_id( 'register_login' ) === $post->ID ) {
            $post_states['register_login'] = __( 'User Register Login Page', 'kc-lang' );
        }

        return $post_states;
    }

    public function kc_get_page_id($page){
        $page = get_option( 'kivicare_' . $page . '_page_id' );
        return $page ? absint( $page ) : -1;
    }

    public function woocommerce_shop_order_add_column(){
        add_action( 'admin_init', function () {
            // Just to make clear how the filters work
            $posttype = ["woocommerce_page_wc-orders",'product' ];

            foreach ($posttype as $post){
                // Priority 20, with 1 parameter (the 1 here is optional)
                add_filter( "manage_edit-{$post}_columns", function ( $columns )use($post) {
                    if($post === 'product'){
                        return $this->woocommerceAddNewColumnInTable(['key' => 'service_id','value' =>esc_html__("Service ID","kc-lang") ],'product_tag',$columns);
                    }else{
                        return $this->woocommerceAddNewColumnInTable(['key' => 'appointment_id','value' =>esc_html__("Appointment ID","kc-lang") ],'order_status',$columns);
                    }
                }, 20, 11 );
                add_filter( "manage_{$post}_columns", function ( $columns )use($post) {
                    if($post === 'product'){
                        return $this->woocommerceAddNewColumnInTable(['key' => 'service_id','value' =>esc_html__("Service ID","kc-lang") ],'product_tag',$columns);
                    }else{
                        return $this->woocommerceAddNewColumnInTable(['key' => 'appointment_id','value' =>esc_html__("Appointment ID","kc-lang") ],'order_status',$columns);
                    }
                }, 20, 11 );

                // Priority 20, with 2 parameters
                add_action( "manage_{$post}_custom_column", function ( $column_name, $order ) use($post){
                    if ( $post === 'woocommerce_page_wc-orders' && 'appointment_id' != $column_name ){
                        return;
                    }
                    if ( $post === 'product' && 'service_id' != $column_name ){
                        return;
                    }
                    $output = "<strong class='order-view'> - </strong>";

                    if( $post === 'product'){
                        $serviceId = get_post_meta($order->get_id(),'kivicare_service_id',true);
                        $doctor_id = get_post_meta($order->get_id(),'kivicare_doctor_id',true);
                        if(!empty($serviceId) && !empty($doctor_id)){
                            $service_details = (new KCService())->get_by(['id' => (int)$serviceId],'=',true);
                            if(!empty($service_details)){
                                $url = esc_url(admin_url('admin.php?page=dashboard#/service/edit/').$serviceId);
                                $serviceId = esc_html('#'.$serviceId);
                                if(!empty($service_details->telemed_service) && $service_details->telemed_service === 'yes'){
                                    $doctor_telemed_active = kcDoctorTelemedServiceEnable($doctor_id);
                                    if($doctor_telemed_active){
                                        $output ="<a href='{$url}' target='_blank' class='order-view'><strong>{$serviceId}</strong></a>";
                                    }
                                }else{
                                    $output ="<a href='{$url}' target='_blank' class='order-view'><strong>{$serviceId}</strong></a>";
                                }
                            }
                        }
                    }else{
                        $appointment_id = get_post_meta($order->get_id(),'kivicare_appointment_id',true);
                        if(!empty($appointment_id)){
                            $appointment_detail = (new KCAppointment())->get_by(['id' => (int)$appointment_id],'=',true);
                            $text = esc_html('#'.$appointment_id.(!empty($appointment_detail->description) ? ' '.$appointment_detail->description : ' ' ));
                            if(!empty($appointment_detail)){
                                $output ="<a  class='order-view'><strong>{$text}</strong></a>";
                            }
                        }
                    }
                    echo $output;
                }, 20, 2 );
            }
        } );

    }

    public function woocommerceAddNewColumnInTable($new_column,$after_column,$columns){
        $order_column_index = array_search($after_column, array_keys($columns)) + 1;
        if(!empty($order_column_index)){
            $total_column = count($columns);
            if($total_column >= $order_column_index){
                return array_slice($columns, 0, $order_column_index, true) +
                    array($new_column['key'] => $new_column['value']) +
                    array_slice($columns, $order_column_index, $total_column - $order_column_index, true);
            }
        }
        return $columns;
    }

    /**
     * Add SignatureRX menu item to dashboard sidebar
     *
     * @param array $sidebar_data The current sidebar data
     * @return array Modified sidebar data with SignatureRX menu item
     */
    public function addSignatureRXToSidebar($sidebar_data) {
        // Create new menu item for SignatureRX
        $signatureRX_item = [
            "label" => "SignatureRX Prescriptions",
            "type" => "route",
            "link" => "signaturerx-prescriptions",
            "iconClass" => "fas fa-prescription",
            "routeClass" => "signaturerx-prescriptions"
        ];

        // Find the right position to insert the item - after Prescription or near medical records
        $insert_position = null;

        foreach ($sidebar_data as $index => $item) {
            // Look for prescription or medical records related items
            if ($item['link'] === 'patient-prescription' ||
                $item['link'] === 'prescription' ||
                $item['link'] === 'medical-records') {
                $insert_position = $index + 1;
                break;
            }
        }

        // If no suitable position found, add to the end
        if ($insert_position === null) {
            $sidebar_data[] = $signatureRX_item;
        } else {
            // Insert the menu item at the specified position
            array_splice($sidebar_data, $insert_position, 0, [$signatureRX_item]);
        }

        return $sidebar_data;
    }

    public function sidebarData() {
        // Retrieve existing sidebar data for different user roles
        $administrator = get_option(KIVI_CARE_PREFIX . 'administrator_dashboard_sidebar_data');
        $clinic_admin = get_option(KIVI_CARE_PREFIX . 'clinic_admin_dashboard_sidebar_data');
        $receptionist = get_option(KIVI_CARE_PREFIX . 'receptionist_dashboard_sidebar_data');
        $doctor = get_option(KIVI_CARE_PREFIX . 'doctor_dashboard_sidebar_data');
        $patient = get_option(KIVI_CARE_PREFIX . 'patient_dashboard_sidebar_data');

        // Generate default sidebar data if not already available
        if (!$administrator) {
            $administrator = kcAdminSidebarArray();
            update_option(KIVI_CARE_PREFIX . 'administrator_dashboard_sidebar_data', $administrator);
        }
        if (!$clinic_admin) {
            $clinic_admin = kcClinicAdminSidebarArray();
            update_option(KIVI_CARE_PREFIX . 'clinic_admin_dashboard_sidebar_data', $clinic_admin);
        }
        if (!$receptionist) {
            $receptionist = kcReceptionistSidebarArray();
            update_option(KIVI_CARE_PREFIX . 'receptionist_dashboard_sidebar_data', $receptionist);
        }
        if (!$doctor) {
            $doctor = kcDoctorSidebarArray();
            update_option(KIVI_CARE_PREFIX . 'doctor_dashboard_sidebar_data', $doctor);
        }
        if (!$patient) {
            $patient = kcPatientSidebarArray();
            update_option(KIVI_CARE_PREFIX . 'patient_dashboard_sidebar_data', $patient);
        }

        // Integrate the tax module into appropriate sections of the sidebar
        if (!get_option(KIVI_CARE_PREFIX . 'tax_module_sidebar3')) {
            // Define a mapping of user roles to their respective sidebar data
            $user_sidebar = [
                KIVI_CARE_PREFIX . 'administrator_dashboard_sidebar_data' => $administrator,
                KIVI_CARE_PREFIX . 'clinic_admin_dashboard_sidebar_data' => $clinic_admin
            ];

            // Define the tax module information
            $tax_module = [
                "label" => "Taxes",
                "type" => "route",
                "link" => "tax",
                "iconClass" => "fas fa-donate",
                "routeClass" => "tax"
            ];

             // Loop through user roles and integrate the tax module into appropriate sections
            foreach ($user_sidebar as $key => &$sidebar) {
                foreach ($sidebar as $tab_key => $tab) {
                    if ($tab['link'] == "tax") {
                        // Tax module link already exists, no need to add
                        continue 2; // Skip to the next user role
                    }
                    if ($tab['link'] == "billings") {
                        // Insert the tax module information into the sidebar
                        array_splice($sidebar, $tab_key + 1, 0, [$tax_module]); // Insert after the 'billings' link
                        update_option($key, $sidebar);
                        break; // Break the loop after integration
                    }
                }
            }

            // Mark the tax module integration as completed
            update_option(KIVI_CARE_PREFIX . 'tax_module_sidebar3', 'yes');
        }
    }

    public function migrateDoctorServiceTableData() {
        // Legacy migration - disabled after services table merge
        if (!get_option(KIVI_CARE_PREFIX.'remove_default_clinic_from_service_table')) {
            // This migration is no longer needed after services table merge
            update_option(KIVI_CARE_PREFIX.'remove_default_clinic_from_service_table', 'yes');
            return;
        }
    }
    public function migrateSidebar()
    {
        if (!get_option(KIVI_CARE_PREFIX . 'migrate_sidebar_item_childrens')) {
            $kc_roles_sidebar = [
                KIVI_CARE_PREFIX . 'administrator_dashboard_sidebar_data',
                KIVI_CARE_PREFIX . 'clinic_admin_dashboard_sidebar_data',
                KIVI_CARE_PREFIX . 'receptionist_dashboard_sidebar_data',
                KIVI_CARE_PREFIX . 'doctor_dashboard_sidebar_data',
            ];
            foreach ($kc_roles_sidebar as $key => $sidebar) {
                if (!empty(get_option($sidebar))) {
                    update_option($sidebar, array_map(function ($row) {
                        if ($row['link'] == "encounter-list") {
                            if (!isset($row['childrens'])) {
                                $row['childrens'] = [
                                    [
                                        'label' => esc_html__('Encounters', 'kc-lang'),
                                        'type' => 'route',
                                        'link' => 'encounter-list',
                                        'iconClass' => 'far fa-calendar-times',
                                        'routeClass' => 'patient_encounter_list',
                                    ],
                                    [
                                        'label' => esc_html__('Encounter Templates', 'kc-lang'),
                                        'type' => 'route',
                                        'link' => 'encounter-template',
                                        'iconClass' => 'far fa-calendar',
                                        'routeClass' => 'encounter_template',
                                    ],
                                ];
                                $row['link'] = "encounter";
                                $row['routeClass'] = "parent";
                                $row['type'] = "parent";
                            }
                        }
                        return $row;
                    }, get_option($sidebar)));
                }
            }

            update_option(KIVI_CARE_PREFIX . 'migrate_sidebar_item_childrens', true);
        }
    }
    /**
     * Load a full-screen template for login and patient login pages
     * This ensures these pages are displayed in full screen regardless of theme settings
     *
     * @param string $template Original template path
     * @return string Modified template path
     */
    public function loadFullScreenLoginTemplate($template) {
        global $post;

        if (!is_object($post)) {
            return $template;
        }

        // Get the IDs of our login pages
        $login_page_id = get_option('kivicare_register_login_page_id');
        $patient_login_page_id = get_option('kivicare_patient_login_page_id');

        // Check if we're on one of our login pages
        if ($post->ID == $login_page_id || $post->ID == $patient_login_page_id) {
            // Load our custom full-screen template
            $fullscreen_template = KIVI_CARE_DIR . 'templates/fullscreen-template.php';

            if (file_exists($fullscreen_template)) {
                return $fullscreen_template;
            }
        }

        return $template;
    }

    /**
     * Redirect WordPress login page to custom login pages based on user roles
     *
     * @param string $login_url The login URL
     * @param string $redirect The redirect URL after login
     * @param bool $force_reauth Whether to force reauthorization
     * @return string Modified login URL
     */

    /**
     * Intercept and redirect all requests to wp-login.php for non-admin users
     * This catches the request earlier than the login_init hook
     */
    public function interceptLoginRequests() {
        // Check if we're accessing wp-login.php
        if (strpos($_SERVER['REQUEST_URI'], 'wp-login.php') !== false) {
            // Allow core WP logout function to work
            if (isset($_GET['action']) && $_GET['action'] === 'logout') {
                return;
            }

            // Allow password reset functionality
            if (isset($_GET['action']) && ($_GET['action'] === 'lostpassword' ||
                                          $_GET['action'] === 'rp' ||
                                          $_GET['action'] === 'resetpass')) {
                return;
            }

            // Allow WordPress admin users
            if (is_user_logged_in() && current_user_can('administrator')) {
                return;
            }

            // For logged in users, redirect based on role
            if (is_user_logged_in()) {
                $user = wp_get_current_user();

                // For patient role
                if (in_array($this->getPatientRole(), $user->roles)) {
                    $patient_login_page_id = get_option('kivicare_patient_login_page_id');
                    if ($patient_login_page_id) {
                        $patient_login_url = get_permalink($patient_login_page_id);
                        if ($patient_login_url) {
                            wp_redirect($patient_login_url);
                            exit;
                        }
                    }
                }

                // For doctor, clinic admin, and receptionist roles
                if (in_array($this->getDoctorRole(), $user->roles) ||
                    in_array($this->getClinicAdminRole(), $user->roles) ||
                    in_array($this->getReceptionistRole(), $user->roles)) {
                    $login_page_id = get_option('kivicare_register_login_page_id');
                    if ($login_page_id) {
                        $custom_login_url = get_permalink($login_page_id);
                        if ($custom_login_url) {
                            wp_redirect($custom_login_url);
                            exit;
                        }
                    }
                }
            }

            // For non-logged-in users, send them to the general login page
            // since we don't know their role yet
            $login_page_id = get_option('kivicare_register_login_page_id');
            if ($login_page_id) {
                $custom_login_url = get_permalink($login_page_id);
                if ($custom_login_url) {
                    wp_redirect($custom_login_url);
                    exit;
                }
            }
        }
    }

    /**
     * Block direct access to wp-login.php for plugin specific roles
     * Redirects them to the appropriate custom login page
     */
    public function blockWpLoginAccess() {
        // Skip for logout action (avoid redirect loop)
        if (isset($_GET['action']) && $_GET['action'] === 'logout') {
            return;
        }

        // Only apply for logged-in users
        if (is_user_logged_in()) {
            $user = wp_get_current_user();

            // For patient role
            if (in_array($this->getPatientRole(), $user->roles)) {
                $patient_login_page_id = get_option('kivicare_patient_login_page_id');
                if ($patient_login_page_id) {
                    $patient_login_url = get_permalink($patient_login_page_id);
                    if ($patient_login_url) {
                        wp_redirect($patient_login_url);
                        exit;
                    }
                }
            }

            // For doctor, clinic admin, and receptionist roles
            if (in_array($this->getDoctorRole(), $user->roles) ||
                in_array($this->getClinicAdminRole(), $user->roles) ||
                in_array($this->getReceptionistRole(), $user->roles)) {
                $login_page_id = get_option('kivicare_register_login_page_id');
                if ($login_page_id) {
                    $custom_login_url = get_permalink($login_page_id);
                    if ($custom_login_url) {
                        wp_redirect($custom_login_url);
                        exit;
                    }
                }
            }
        }
    }

    public function redirectLoginPage($login_url, $redirect, $force_reauth) {
        // If the user has a role, determine which login page to use
        if (is_user_logged_in()) {
            $user = wp_get_current_user();

            // If it's a patient, use patient login page
            if (in_array($this->getPatientRole(), $user->roles)) {
                $patient_login_page_id = get_option('kivicare_patient_login_page_id');
                if ($patient_login_page_id) {
                    $patient_login_url = get_permalink($patient_login_page_id);
                    if ($patient_login_url) {
                        return $patient_login_url;
                    }
                }
            }

            // For doctor, clinic admin, and receptionist use regular login page
            if (in_array($this->getDoctorRole(), $user->roles) ||
                in_array($this->getClinicAdminRole(), $user->roles) ||
                in_array($this->getReceptionistRole(), $user->roles)) {
                $login_page_id = get_option('kivicare_register_login_page_id');
                if ($login_page_id) {
                    $custom_login_url = get_permalink($login_page_id);
                    if ($custom_login_url) {
                        return $custom_login_url;
                    }
                }
            }

            // If we're on wp-login.php, check the current user's role to determine where to redirect
            if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'wp-login.php') !== false) {
                // For patients
                if (in_array($this->getPatientRole(), $user->roles)) {
                    $patient_login_page_id = get_option('kivicare_patient_login_page_id');
                    if ($patient_login_page_id) {
                        $patient_login_url = get_permalink($patient_login_page_id);
                        if ($patient_login_url) {
                            wp_redirect($patient_login_url);
                            exit;
                        }
                    }
                }

                // For doctor, clinic admin, and receptionist roles
                if (in_array($this->getDoctorRole(), $user->roles) ||
                    in_array($this->getClinicAdminRole(), $user->roles) ||
                    in_array($this->getReceptionistRole(), $user->roles)) {
                    $login_page_id = get_option('kivicare_register_login_page_id');
                    if ($login_page_id) {
                        $custom_login_url = get_permalink($login_page_id);
                        if ($custom_login_url) {
                            wp_redirect($custom_login_url);
                            exit;
                        }
                    }
                }
            }
        } else {
            // For users not logged in yet, we'll use the default login page initially
            // We'll let the redirection after login happen based on roles
        }

        return $login_url;
    }

    /**
     * Restrict access to wp-admin for non-admin roles and redirect to appropriate pages
     * @return void
     */
    public function restrictNonAdminAccess() {
        // Only apply this restriction if we're in the admin area
        if (!is_admin()) {
            return;
        }

        // Get current user
        $user = wp_get_current_user();

        // Skip for AJAX, Heartbeat API, and async uploads to prevent breaking functionality
        if (wp_doing_ajax() || isset($_REQUEST['action']) && ($_REQUEST['action'] == 'heartbeat' || $_REQUEST['action'] == 'upload-attachment')) {
            return;
        }

        // Get the page slug
        $page = isset($_GET['page']) ? $_GET['page'] : '';

        // If user is not logged in, skip
        if (!is_user_logged_in()) {
            return;
        }

        // Allow access to Kivicare dashboard
        if ($page === 'dashboard') {
            return;
        }

        // Allow admin users full access
        if (current_user_can('administrator')) {
            return;
        }

        // Restrict access based on user role
        if (in_array($this->getDoctorRole(), $user->roles) ||
            in_array($this->getClinicAdminRole(), $user->roles) ||
            in_array($this->getReceptionistRole(), $user->roles)) {
            // Redirect to the login page
            $login_page_id = get_option('kivicare_register_login_page_id');
            if ($login_page_id) {
                $login_url = get_permalink($login_page_id);
                if ($login_url) {
                    wp_redirect($login_url);
                    exit;
                }
            }

            // Fallback to Kivicare dashboard if login page ID is not set
            wp_redirect(admin_url('admin.php?page=dashboard'));
            exit;
        }

        if (in_array($this->getPatientRole(), $user->roles)) {
            // Redirect to the patient login page
            $patient_login_page_id = get_option('kivicare_patient_login_page_id');
            if ($patient_login_page_id) {
                $patient_login_url = get_permalink($patient_login_page_id);
                if ($patient_login_url) {
                    wp_redirect($patient_login_url);
                    exit;
                }
            }

            // Fallback to Kivicare dashboard if patient login page ID is not set
            wp_redirect(admin_url('admin.php?page=dashboard'));
            exit;
        }
    }
}
