<?php
/**
 * Migration: Create Task Management Feature Tables
 * File: create_task_management_feature_table.php
 */

if (!defined('ABSPATH')) {
    exit;
}

class CreateTaskManagementFeatureTable {
    /**
     * Run the migration - creates all tables for task management
     */
    public function up() {
        global $wpdb;

        error_log("[Migration] Starting task tables creation");

        $charset_collate = $wpdb->get_charset_collate();

        // Define table names
        $tasks_table = $wpdb->prefix . 'kc_tasks';
        $assignees_table = $wpdb->prefix . 'kc_task_assignees';
        $comments_table = $wpdb->prefix . 'kc_task_comments';
        $attachments_table = $wpdb->prefix . 'kc_task_attachments';

        // 1. Tasks Table
        $tasks_sql = "CREATE TABLE IF NOT EXISTS {$tasks_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            clinic_id bigint(20) UNSIGNED NOT NULL,
            creator_id bigint(20) UNSIGNED NOT NULL,
            patient_id bigint(20) UNSIGNED DEFAULT NULL,
            priority ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
            status ENUM('pending', 'in_progress', 'completed', 'archived') NOT NULL DEFAULT 'pending',
            due_date datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            reminder_date datetime DEFAULT NULL,
            repeating ENUM('none', 'daily', 'weekly', 'monthly') DEFAULT 'none',
            category varchar(100) DEFAULT NULL,
            is_archived TINYINT(1) DEFAULT 0,
            PRIMARY KEY (id),
            INDEX clinic_id (clinic_id),
            INDEX creator_id (creator_id),
            INDEX patient_id (patient_id),
            INDEX status (status),
            INDEX due_date (due_date)
        ) {$charset_collate};";

        // 2. Task Assignees Table
        $assignees_sql = "CREATE TABLE IF NOT EXISTS {$assignees_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            task_id bigint(20) UNSIGNED NOT NULL,
            assignee_id bigint(20) UNSIGNED NOT NULL,
            assigned_at datetime DEFAULT CURRENT_TIMESTAMP,
            completed_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY task_assignee (task_id, assignee_id),
            INDEX task_id (task_id),
            INDEX assignee_id (assignee_id)
        ) {$charset_collate};";

        // 3. Task Comments Table
        $comments_sql = "CREATE TABLE IF NOT EXISTS {$comments_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            task_id bigint(20) UNSIGNED NOT NULL,
            user_id bigint(20) UNSIGNED NOT NULL,
            comment text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX task_id (task_id),
            INDEX user_id (user_id)
        ) {$charset_collate};";

        // 4. Task Attachments Table
        $attachments_sql = "CREATE TABLE IF NOT EXISTS {$attachments_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            task_id bigint(20) UNSIGNED NOT NULL,
            file_name varchar(255) NOT NULL,
            file_url varchar(255) NOT NULL,
            attachment_id bigint(20) UNSIGNED NOT NULL,
            uploaded_by bigint(20) UNSIGNED NOT NULL,
            uploaded_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX task_id (task_id),
            INDEX uploaded_by (uploaded_by),
            INDEX attachment_id (attachment_id)
        ) {$charset_collate};";

        // Execute all CREATE TABLE queries
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($tasks_sql);
        dbDelta($assignees_sql);
        dbDelta($comments_sql);
        dbDelta($attachments_sql);

        error_log("[Migration] Task management tables created successfully");
        return true;
    }

    /**
     * Reverse the migration - drops all task management tables
     */
    public function down() {
        global $wpdb;

        error_log("[Migration] Dropping task management tables");

        // Drop tables in reverse order to handle dependencies
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_task_attachments");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_task_comments");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_task_assignees");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tasks");

        error_log("[Migration] Task management tables dropped successfully");
        return true;
    }
}
