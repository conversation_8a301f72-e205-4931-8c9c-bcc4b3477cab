<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTDLTestRequest extends KCModel {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct('tdl_test_requests');
    }

    /**
     * Get all test requests with optional filtering
     *
     * @param array $args Optional arguments for pagination and filtering
     * @return array List of test requests with pagination data
     */
    public function getRequests($args = []) {
        global $wpdb;
        
        $defaults = [
            'page' => 1,
            'per_page' => 25,
            'clinic_id' => null,
            'patient_id' => null,
            'doctor_id' => null,
            'status' => null,
            'order_number' => null,
            'date_from' => null,
            'date_to' => null,
            'orderby' => 'created_at',
            'order' => 'DESC',
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['patient_id'])) {
            $where .= ' AND patient_id = %d';
            $params[] = (int)$args['patient_id'];
        }
        
        if (!empty($args['doctor_id'])) {
            $where .= ' AND doctor_id = %d';
            $params[] = (int)$args['doctor_id'];
        }
        
        if (!empty($args['status'])) {
            $where .= ' AND status = %s';
            $params[] = sanitize_text_field($args['status']);
        }
        
        if (!empty($args['order_number'])) {
            $where .= ' AND order_number = %s';
            $params[] = sanitize_text_field($args['order_number']);
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND created_at >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND created_at <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Search functionality
        if (!empty($args['search'])) {
            $search = sanitize_text_field($args['search']);
            $where .= ' AND (order_number LIKE %s OR clinical_notes LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        // Handle ordering
        $orderby = $args['orderby'];
        $order = $args['order'];
        
        // Validate order parameters
        $valid_orderby = ['id', 'order_number', 'status', 'collection_date', 'created_at', 'updated_at'];
        if (!in_array($orderby, $valid_orderby)) {
            $orderby = 'created_at';
        }
        
        $order = strtoupper($order);
        if ($order !== 'ASC' && $order !== 'DESC') {
            $order = 'DESC';
        }
        
        // First get total count for pagination
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        $total = (int)$wpdb->get_var($wpdb->prepare($count_query, $params));
        
        // Now get the actual results with pagination
        $limit = (int)$args['per_page'];
        $offset = (int)(($args['page'] - 1) * $limit);
        
        $query = "SELECT * FROM {$table_name} WHERE {$where} ORDER BY {$orderby} {$order} LIMIT %d OFFSET %d";
        $params[] = $limit;
        $params[] = $offset;
        
        $requests = $wpdb->get_results($wpdb->prepare($query, $params), ARRAY_A);
        
        // Return both results and pagination data
        return [
            'requests' => $requests,
            'total' => $total,
            'page' => (int)$args['page'],
            'per_page' => (int)$args['per_page'],
            'total_pages' => ceil($total / $args['per_page'])
        ];
    }

    /**
     * Count total test requests with optional filtering
     *
     * @param array $args Optional filter arguments
     * @return int Number of requests
     */
    public function countRequests($args = []) {
        global $wpdb;
        
        $defaults = [
            'clinic_id' => null,
            'patient_id' => null,
            'doctor_id' => null,
            'status' => null,
            'order_number' => null,
            'date_from' => null,
            'date_to' => null,
            'search' => '',
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['patient_id'])) {
            $where .= ' AND patient_id = %d';
            $params[] = (int)$args['patient_id'];
        }
        
        if (!empty($args['doctor_id'])) {
            $where .= ' AND doctor_id = %d';
            $params[] = (int)$args['doctor_id'];
        }
        
        if (!empty($args['status'])) {
            $where .= ' AND status = %s';
            $params[] = sanitize_text_field($args['status']);
        }
        
        if (!empty($args['order_number'])) {
            $where .= ' AND order_number = %s';
            $params[] = sanitize_text_field($args['order_number']);
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND created_at >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND created_at <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Search functionality
        if (!empty($args['search'])) {
            $search = sanitize_text_field($args['search']);
            $where .= ' AND (order_number LIKE %s OR clinical_notes LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        $query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        
        return (int)$wpdb->get_var($wpdb->prepare($query, $params));
    }

    /**
     * Get a single test request by ID with extended information
     *
     * @param int $request_id Request ID
     * @return array|false Request data or false if not found
     */
    public function getRequestById($request_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $request_id = (int)$request_id;
        
        // Get the basic request data
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d", 
            $request_id
        );
        
        $request = $wpdb->get_row($query, ARRAY_A);
        
        if (!$request) {
            return false;
        }
        
        // Get associated test items
        $items_table = $wpdb->prefix . 'kc_tdl_test_request_items';
        $items_query = $wpdb->prepare(
            "SELECT * FROM {$items_table} WHERE request_id = %d ORDER BY id ASC",
            $request_id
        );
        
        $request['items'] = $wpdb->get_results($items_query, ARRAY_A);
        
        return $request;
    }

    /**
     * Get a test request by order number
     *
     * @param string $order_number Order number
     * @return array|false Request data or false if not found
     */
    public function getRequestByOrderNumber($order_number) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $order_number = sanitize_text_field($order_number);
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE order_number = %s", 
            $order_number
        );
        
        return $wpdb->get_row($query, ARRAY_A);
    }

    /**
     * Create a new test request
     *
     * @param array $request_data Request data
     * @return int|false ID of new request or false on failure
     */
    public function createRequest($request_data) {
        if (empty($request_data['clinic_id']) || empty($request_data['patient_id']) || 
            empty($request_data['order_number'])) {
            return false;
        }
        
        // Sanitize data
        $data_to_save = [
            'clinic_id' => (int)$request_data['clinic_id'],
            'patient_id' => (int)$request_data['patient_id'],
            'doctor_id' => isset($request_data['doctor_id']) ? (int)$request_data['doctor_id'] : null,
            'order_number' => sanitize_text_field($request_data['order_number']),
            'status' => isset($request_data['status']) ? sanitize_text_field($request_data['status']) : 'pending',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];
        
        // Optional fields
        if (!empty($request_data['collection_date'])) {
            $data_to_save['collection_date'] = sanitize_text_field($request_data['collection_date']);
        }
        
        if (!empty($request_data['clinical_notes'])) {
            $data_to_save['clinical_notes'] = sanitize_textarea_field($request_data['clinical_notes']);
        }
        
        if (!empty($request_data['hl7_message'])) {
            $data_to_save['hl7_message'] = $request_data['hl7_message']; // Raw HL7 message - intentionally not sanitized
        }
        
        $result = $this->insert($data_to_save);
        
        if ($result) {
            return $result;
        }
        
        return false;
    }

    /**
     * Update an existing test request
     *
     * @param int $request_id Request ID
     * @param array $request_data Request data to update
     * @return bool Success or failure
     */
    public function updateRequest($request_id, $request_data) {
        $request_id = (int)$request_id;
        
        if (empty($request_id)) {
            return false;
        }
        
        // Check if request exists
        $existing = $this->getRequestById($request_id);
        if (!$existing) {
            return false;
        }
        
        // Prepare data to update
        $data_to_update = ['updated_at' => current_time('mysql')];
        
        // Only include fields that are provided
        if (isset($request_data['status'])) {
            $data_to_update['status'] = sanitize_text_field($request_data['status']);
        }
        
        if (isset($request_data['clinical_notes'])) {
            $data_to_update['clinical_notes'] = sanitize_textarea_field($request_data['clinical_notes']);
        }
        
        if (isset($request_data['collection_date'])) {
            $data_to_update['collection_date'] = sanitize_text_field($request_data['collection_date']);
        }
        
        if (isset($request_data['doctor_id'])) {
            $data_to_update['doctor_id'] = (int)$request_data['doctor_id'];
        }
        
        return $this->update($data_to_update, ['id' => $request_id]);
    }

    /**
     * Generate a unique order number for test requests
     *
     * @param int $clinic_id Clinic ID
     * @return string Unique order number (max 10 chars)
     */
    public function generateOrderNumber($clinic_id) {
        global $wpdb;
        
        $prefix = 'TDL';
        $clinic_part = sprintf('%02d', (int)$clinic_id % 100); // Last 2 digits of clinic_id
        $date_part = date('ymd'); // Current date in YYMMDD format
        
        $table_name = $this->get_table_name();
        
        // Get the number of requests for this clinic today
        $today_start = date('Y-m-d 00:00:00');
        
        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE clinic_id = %d AND created_at >= %s",
            (int)$clinic_id,
            $today_start
        );
        
        $count = (int)$wpdb->get_var($query);
        $sequence = sprintf('%03d', ($count + 1) % 1000); // 3-digit sequence number
        
        return $prefix . $clinic_part . $date_part . $sequence;
    }
    
    /**
     * Get test requests statistics
     *
     * @param array $args Optional filter arguments
     * @return array Statistics about test requests
     */
    public function getRequestsStats($args = []) {
        global $wpdb;
        
        $defaults = [
            'clinic_id' => null,
            'date_from' => null,
            'date_to' => null,
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND created_at >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND created_at <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Get total count
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        $total = (int)$wpdb->get_var($wpdb->prepare($count_query, $params));
        
        // Get counts by status
        $status_query = "SELECT status, COUNT(*) as count FROM {$table_name} 
                         WHERE {$where} GROUP BY status";
        $status_counts = $wpdb->get_results($wpdb->prepare($status_query, $params), ARRAY_A);
        
        // Format status counts
        $status_stats = [
            'pending' => 0,
            'sent' => 0,
            'in_progress' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];
        
        foreach ($status_counts as $status) {
            $status_stats[$status['status']] = (int)$status['count'];
        }
        
        // Get requests by day for the past month
        $date_from = !empty($args['date_from']) ? $args['date_from'] : date('Y-m-d', strtotime('-30 days'));
        $date_to = !empty($args['date_to']) ? $args['date_to'] : date('Y-m-d');
        
        $params = [];
        if (!empty($args['clinic_id'])) {
            $where = 'clinic_id = %d AND';
            $params[] = (int)$args['clinic_id'];
        } else {
            $where = '';
        }
        
        $params[] = $date_from;
        $params[] = $date_to;
        
        $daily_query = $wpdb->prepare(
            "SELECT DATE(created_at) as date, COUNT(*) as count 
             FROM {$table_name} 
             WHERE {$where} created_at BETWEEN %s AND %s 
             GROUP BY DATE(created_at) 
             ORDER BY date ASC",
            $params
        );
        
        $daily_counts = $wpdb->get_results($daily_query, ARRAY_A);
        
        return [
            'total' => $total,
            'by_status' => $status_stats,
            'daily' => $daily_counts
        ];
    }
    
    /**
     * Delete a test request
     *
     * @param int $request_id Request ID
     * @return bool Success or failure
     */
    public function deleteRequest($request_id) {
        global $wpdb;
        
        $request_id = (int)$request_id;
        
        // First delete all related items
        $items_table = $wpdb->prefix . 'kc_tdl_test_request_items';
        $wpdb->delete($items_table, ['request_id' => $request_id]);
        
        // Then delete the request itself
        return $this->delete(['id' => $request_id]);
    }
}