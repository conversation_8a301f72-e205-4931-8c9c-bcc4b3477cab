<?php
/**
 * Migration: Create Notifications Tables
 * File: create_notifications_table.php
 */

if (!defined('ABSPATH')) {
    exit;
}

class CreateNotificationsTable {
    /**
     * Run the migration - creates notifications table
     */
    public function up() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Define table name
        $notifications_table = $wpdb->prefix . 'kc_notifications';

        // Notifications Table
        $notifications_sql = "CREATE TABLE IF NOT EXISTS {$notifications_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            message text NOT NULL,
            type varchar(50) NOT NULL DEFAULT 'info',
            reference_id bigint(20) DEFAULT NULL,
            reference_type varchar(100) DEFAULT NULL,
            is_read tinyint(1) NOT NULL DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY is_read (is_read),
            KEY reference_id (reference_id),
            KEY reference_type (reference_type)
        ) {$charset_collate};";

        // Execute CREATE TABLE query
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($notifications_sql);

        // Update version tracking
        update_option('kivicare_notifications_table_version', KIVI_CARE_VERSION);

        error_log("[Migration] Notifications table created successfully");
        return true;
    }

    /**
     * Reverse the migration - drops notifications table
     */
    public function down() {
        global $wpdb;

        error_log("[Migration] Dropping notifications table");

        // Drop the table
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_notifications");

        // Remove version tracking
        delete_option('kivicare_notifications_table_version');

        error_log("[Migration] Notifications table dropped successfully");
        return true;
    }
}