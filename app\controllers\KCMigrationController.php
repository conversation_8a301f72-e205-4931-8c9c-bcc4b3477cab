<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;

class KC<PERSON>igrationController extends KCBase {

    public $db;
    private $request;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        parent::__construct();

        // Only allow administrators to run migrations
        if ($this->getLoginUserRole() !== 'administrator') {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
    }

    /**
     * Run database migrations
     */
    public function runMigrations() {
        global $kc_migration;

        try {
            // Create migration tracking table if needed
            $kc_migration->create_migrations_table();
            
            // Run all pending migrations
            $result = $kc_migration->run_migrations();
            
            // Clear the migrations needed flag
            delete_option('kivicare_migrations_needed');

            wp_send_json([
                'status' => true,
                'message' => 'Migrations executed successfully',
                'data' => []
            ]);

        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => 'Migration failed: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Check if migrations are needed
     */
    public function checkMigrations() {
        global $kc_migration;

        try {
            // Check if there are pending migrations
            $pending_migrations = $kc_migration->get_pending_migrations();
            
            wp_send_json([
                'status' => true,
                'message' => 'Migration status checked',
                'data' => [
                    'pending_count' => count($pending_migrations),
                    'pending_migrations' => $pending_migrations
                ]
            ]);

        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => 'Failed to check migration status: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}
