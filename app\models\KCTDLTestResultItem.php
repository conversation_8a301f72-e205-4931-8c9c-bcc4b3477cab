<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTDLTestResultItem extends KCModel {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct('tdl_test_result_items');
    }

    /**
     * Get all result items for a result
     *
     * @param int $result_id Result ID
     * @return array Result items
     */
    public function getItemsByResultId($result_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $result_id = (int)$result_id;
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE result_id = %d ORDER BY test_name, biomarker_name", 
            $result_id
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }

    /**
     * Get a specific result item by ID
     *
     * @param int $item_id Item ID
     * @return array|false Item data or false if not found
     */
    public function getItemById($item_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $item_id = (int)$item_id;
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d", 
            $item_id
        );
        
        return $wpdb->get_row($query, ARRAY_A);
    }

    /**
     * Add a result item
     *
     * @param array $item_data Item data
     * @return int|false ID of new item or false on failure
     */
    public function addItem($item_data) {
        if (empty($item_data['result_id']) || empty($item_data['test_code']) || 
            empty($item_data['test_name']) || empty($item_data['biomarker_name'])) {
            return false;
        }
        
        // Sanitize data
        $data_to_save = [
            'result_id' => (int)$item_data['result_id'],
            'test_code' => sanitize_text_field($item_data['test_code']),
            'test_name' => sanitize_text_field($item_data['test_name']),
            'biomarker_name' => sanitize_text_field($item_data['biomarker_name']),
            'created_at' => current_time('mysql')
        ];
        
        // Optional fields
        if (isset($item_data['value'])) {
            $data_to_save['value'] = sanitize_text_field($item_data['value']);
        }
        
        if (isset($item_data['units'])) {
            $data_to_save['units'] = sanitize_text_field($item_data['units']);
        }
        
        if (isset($item_data['reference_range'])) {
            $data_to_save['reference_range'] = sanitize_text_field($item_data['reference_range']);
        }
        
        if (isset($item_data['abnormal_flag'])) {
            $data_to_save['abnormal_flag'] = sanitize_text_field($item_data['abnormal_flag']);
        }
        
        if (isset($item_data['observation_datetime'])) {
            $data_to_save['observation_datetime'] = sanitize_text_field($item_data['observation_datetime']);
        }
        
        return $this->insert($data_to_save);
    }

    /**
     * Update a result item
     *
     * @param int $item_id Item ID
     * @param array $item_data Item data to update
     * @return bool Success or failure
     */
    public function updateItem($item_id, $item_data) {
        $item_id = (int)$item_id;
        
        if (empty($item_id)) {
            return false;
        }
        
        // Check if item exists
        $existing = $this->getItemById($item_id);
        if (!$existing) {
            return false;
        }
        
        // Prepare data to update
        $data_to_update = ['updated_at' => current_time('mysql')];
        
        // Only include fields that are provided
        if (isset($item_data['test_name'])) {
            $data_to_update['test_name'] = sanitize_text_field($item_data['test_name']);
        }
        
        if (isset($item_data['biomarker_name'])) {
            $data_to_update['biomarker_name'] = sanitize_text_field($item_data['biomarker_name']);
        }
        
        if (isset($item_data['value'])) {
            $data_to_update['value'] = sanitize_text_field($item_data['value']);
        }
        
        if (isset($item_data['units'])) {
            $data_to_update['units'] = sanitize_text_field($item_data['units']);
        }
        
        if (isset($item_data['reference_range'])) {
            $data_to_update['reference_range'] = sanitize_text_field($item_data['reference_range']);
        }
        
        if (isset($item_data['abnormal_flag'])) {
            $data_to_update['abnormal_flag'] = sanitize_text_field($item_data['abnormal_flag']);
        }
        
        if (isset($item_data['observation_datetime'])) {
            $data_to_update['observation_datetime'] = sanitize_text_field($item_data['observation_datetime']);
        }
        
        return $this->update($data_to_update, ['id' => $item_id]);
    }

    /**
     * Delete all items for a result
     *
     * @param int $result_id Result ID
     * @return bool Success or failure
     */
    public function deleteItemsByResultId($result_id) {
        $result_id = (int)$result_id;
        
        if (empty($result_id)) {
            return false;
        }
        
        return $this->delete(['result_id' => $result_id]);
    }

    /**
     * Delete a specific result item
     *
     * @param int $item_id Item ID
     * @return bool Success or failure
     */
    public function deleteItem($item_id) {
        $item_id = (int)$item_id;
        
        if (empty($item_id)) {
            return false;
        }
        
        return $this->delete(['id' => $item_id]);
    }

    /**
     * Get tests grouped by test_code for a result
     *
     * @param int $result_id Result ID
     * @return array Tests grouped by test_code
     */
    public function getTestsGroupedByCode($result_id) {
        $items = $this->getItemsByResultId($result_id);
        $grouped = [];
        
        foreach ($items as $item) {
            if (!isset($grouped[$item['test_code']])) {
                $grouped[$item['test_code']] = [
                    'test_code' => $item['test_code'],
                    'test_name' => $item['test_name'],
                    'biomarkers' => []
                ];
            }
            
            $grouped[$item['test_code']]['biomarkers'][] = [
                'id' => $item['id'],
                'name' => $item['biomarker_name'],
                'value' => $item['value'],
                'units' => $item['units'],
                'reference_range' => $item['reference_range'],
                'abnormal_flag' => $item['abnormal_flag'],
                'observation_datetime' => $item['observation_datetime'],
            ];
        }
        
        return array_values($grouped);
    }
    
    /**
     * Get abnormal biomarkers for a result
     *
     * @param int $result_id Result ID
     * @return array Abnormal biomarkers
     */
    public function getAbnormalBiomarkers($result_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $result_id = (int)$result_id;
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} 
             WHERE result_id = %d 
             AND abnormal_flag IS NOT NULL 
             AND abnormal_flag != '' 
             ORDER BY test_name, biomarker_name", 
            $result_id
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }
    
    /**
     * Get biomarker history for a patient
     *
     * @param int $patient_id Patient ID
     * @param string $biomarker_name Biomarker name
     * @param int $limit Optional limit of results to return
     * @return array Biomarker history
     */
    public function getBiomarkerHistory($patient_id, $biomarker_name, $limit = 10) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $results_table = $wpdb->prefix . 'kc_tdl_test_results';
        
        $patient_id = (int)$patient_id;
        $biomarker_name = sanitize_text_field($biomarker_name);
        $limit = (int)$limit;
        
        $query = $wpdb->prepare(
            "SELECT i.*, r.result_date, r.patient_id 
             FROM {$table_name} i
             JOIN {$results_table} r ON i.result_id = r.id
             WHERE r.patient_id = %d 
             AND i.biomarker_name = %s
             ORDER BY r.result_date DESC
             LIMIT %d", 
            $patient_id,
            $biomarker_name,
            $limit
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }
    
    /**
     * Bulk add result items
     *
     * @param int $result_id Result ID
     * @param array $items Array of item data
     * @return array Results with counts of added and failed items
     */
    public function bulkAddItems($result_id, $items) {
        $result_id = (int)$result_id;
        
        if (empty($result_id) || !is_array($items) || empty($items)) {
            return [
                'added' => 0,
                'failed' => 0
            ];
        }
        
        $added = 0;
        $failed = 0;
        
        foreach ($items as $item) {
            // Make sure item has result_id set
            $item['result_id'] = $result_id;
            
            $result = $this->addItem($item);
            
            if ($result) {
                $added++;
            } else {
                $failed++;
            }
        }
        
        return [
            'added' => $added,
            'failed' => $failed
        ];
    }
}