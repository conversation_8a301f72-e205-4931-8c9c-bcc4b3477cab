<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCTDLClinicSetting;
use App\models\KCTDLTestCatalog;
use App\models\KCTDLTestRequest;
use App\models\KCTDLTestRequestItem;
use App\models\KCTDLTestResult;
use App\models\KCTDLTestResultItem;
use App\models\KCClinic;
use App\models\KCUser;
use Exception;

class KCTDLLabsController extends KCBase {
    public $db;

    private $tdl_clinic_setting;
    private $tdl_test_catalog;
    private $tdl_test_request;
    private $tdl_test_request_item;
    private $tdl_test_result;
    private $tdl_test_result_item;
    private $clinic_model;
    private $patient_model;
    private $user_model;
    private $request;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        
        $this->tdl_clinic_setting = new KCTDLClinicSetting();
        $this->tdl_test_catalog = new KCTDLTestCatalog();
        $this->tdl_test_request = new KCTDLTestRequest();
        $this->tdl_test_request_item = new KCTDLTestRequestItem();
        $this->tdl_test_result = new KCTDLTestResult();
        $this->tdl_test_result_item = new KCTDLTestResultItem();
        $this->clinic_model = new KCClinic();
        $this->user_model = new KCUser();
        
        parent::__construct();
    }

    /**
     * Process HL7 results
     *
     * @param string $hl7_content HL7 message content
     * @param int $clinic_id Clinic ID
     * @return int Result ID
     * @throws Exception
     */
    private function processHL7Results($hl7_content, $clinic_id) {
        // This would be a complex implementation in a real-world scenario
        // Here we just throw an "Not implemented" exception
        throw new Exception(esc_html__('HL7 processing not implemented in this version', 'kc-lang'));
    }
    /**
     * Review a test result
     */
    public function reviewTestResult() {
        try {
            $request_data = $this->request->getInputs();
            
            if (empty($request_data['result_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Result ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            if (empty($request_data['reviewer_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Reviewer ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            $result_id = (int) $request_data['result_id'];
            $reviewer_id = (int) $request_data['reviewer_id'];
            $notes = isset($request_data['notes']) ? sanitize_textarea_field($request_data['notes']) : '';
            
            $result = $this->tdl_test_result->getResultById($result_id);
            
            if (empty($result)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Result not found', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            $success = $this->tdl_test_result->markAsReviewed($result_id, $reviewer_id, $notes);
            
            if (!$success) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to mark result as reviewed', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Result marked as reviewed', 'kc-lang'),
                'data' => []
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Poll Azure Blob Storage for new results
     */
    public function pollAzureForResults() {
        try {
            $request_data = $this->request->getInputs();
            $clinic_id = isset($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : 0;
            
            if (empty($clinic_id)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Clinic ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // Get clinic TDL settings
            $tdl_settings = $this->tdl_clinic_setting->getClinicSettings($clinic_id);
            
            if (empty($tdl_settings) || empty($tdl_settings['azure_storage_connection']) || empty($tdl_settings['azure_storage_container'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Azure storage settings not configured', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // TODO: Implement Azure Blob Storage polling using the MicrosoftAzure PHP SDK
            // This would be implemented in a real-world scenario
            
            // For demo, just return success with 0 new results
            wp_send_json([
                'status' => true,
                'message' => esc_html__('No new results found', 'kc-lang'),
                'data' => [
                    'new_results' => 0,
                ]
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Process JSON results
     *
     * @param string $json_content JSON content
     * @param int $clinic_id Clinic ID
     * @return int Result ID
     * @throws Exception
     */
    private function processJSONResults($json_content, $clinic_id) {
        $data = json_decode($json_content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception(esc_html__('Invalid JSON content', 'kc-lang'));
        }
        
        // Extract required fields from JSON
        if (empty($data['patient_id'])) {
            throw new Exception(esc_html__('Patient ID is required in JSON data', 'kc-lang'));
        }
        
        // Create result record
        $result_data = [
            'patient_id' => (int) $data['patient_id'],
            'doctor_id' => !empty($data['doctor_id']) ? (int) $data['doctor_id'] : null,
            'clinic_id' => $clinic_id,
            'order_number' => $data['order_number'] ?? null,
            'result_id' => $data['result_id'] ?? 'TDL_' . uniqid(),
            'result_date' => $data['result_date'] ?? current_time('mysql'),
            'result_status' => 'received',
            'result_data' => $json_content,
        ];
        
        // Check if this is linked to an existing request
        if (!empty($data['order_number'])) {
            $request = $this->tdl_test_request->getRequestByOrderNumber($data['order_number']);
            if ($request) {
                $result_data['request_id'] = $request['id'];
                
                // Update request status
                $this->tdl_test_request->updateRequest($request['id'], ['status' => 'completed']);
            }
        }
        
        // Create result
        $result_id = $this->tdl_test_result->createResult($result_data);
        
        if (!$result_id) {
            throw new Exception(esc_html__('Failed to create result record', 'kc-lang'));
        }
        
        // Process test panels and biomarkers
        if (!empty($data['panels']) && is_array($data['panels'])) {
            foreach ($data['panels'] as $panel) {
                $test_code = $panel['code'] ?? '';
                $test_name = $panel['name'] ?? '';
                
                if (empty($test_code) || empty($test_name)) {
                    continue;
                }
                
                // Process biomarkers
                if (!empty($panel['biomarkers']) && is_array($panel['biomarkers'])) {
                    foreach ($panel['biomarkers'] as $biomarker) {
                        $biomarker_name = $biomarker['name'] ?? '';
                        $value = $biomarker['value'] ?? '';
                        
                        if (empty($biomarker_name) || $value === '') {
                            continue;
                        }
                        
                        $item_data = [
                            'result_id' => $result_id,
                            'test_code' => $test_code,
                            'test_name' => $test_name,
                            'biomarker_name' => $biomarker_name,
                            'value' => $value,
                            'units' => $biomarker['units'] ?? '',
                            'reference_range' => $biomarker['reference_range'] ?? '',
                            'abnormal_flag' => $biomarker['abnormal_flag'] ?? null,
                            'observation_datetime' => $biomarker['observation_datetime'] ?? $data['result_date'] ?? current_time('mysql'),
                        ];
                        
                        $this->tdl_test_result_item->addItem($item_data);
                    }
                }
            }
        }
        
        return $result_id;
    }

    /**
     * Import test results from HL7 or JSON
     */
    public function importTestResults() {
        try {
            $request_data = $this->request->getInputs();
            
            if (empty($request_data['clinic_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Clinic ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            if (empty($request_data['format']) || !in_array($request_data['format'], ['hl7', 'json'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Valid format is required (hl7 or json)', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            if (empty($request_data['content'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Result content is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            $clinic_id = (int) $request_data['clinic_id'];
            $format = sanitize_text_field($request_data['format']);
            $content = $request_data['content'];
            
            // Process based on format
            if ($format === 'hl7') {
                $result_id = $this->processHL7Results($content, $clinic_id);
            } else {
                $result_id = $this->processJSONResults($content, $clinic_id);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Results imported successfully', 'kc-lang'),
                'data' => [
                    'result_id' => $result_id,
                ]
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Get clinic TDL settings
     */
    public function getClinicSettings() {
        try {
            $request_data = $this->request->getInputs();
            $clinic_id = isset($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : 0;
            
            if (empty($clinic_id)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Clinic ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            $settings = $this->tdl_clinic_setting->getClinicSettings($clinic_id);
            
            if (empty($settings)) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('No settings found for this clinic', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // Remove sensitive information from response
            unset($settings['api_key']);
            unset($settings['azure_storage_connection']);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Settings retrieved successfully', 'kc-lang'),
                'data' => $settings
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Save clinic TDL settings
     */
    public function saveClinicSettings() {
        try {
            $request_data = $this->request->getInputs();
            
            // Validate required fields
            $required_fields = ['clinic_id', 'account_id', 'api_key', 'sender_id'];
            foreach ($required_fields as $field) {
                if (empty($request_data[$field])) {
                    wp_send_json([
                        'status' => false,
                        'message' => sprintf(esc_html__('%s is required', 'kc-lang'), ucfirst(str_replace('_', ' ', $field))),
                        'data' => []
                    ]);
                }
            }
            
            $result = $this->tdl_clinic_setting->saveClinicSettings($request_data);
            
            if (!$result) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to save settings', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Settings saved successfully', 'kc-lang'),
                'data' => []
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Get TDL tests catalog
     */
    public function getTestsCatalog() {
        try {
            $request_data = $this->request->getInputs();
            
            $page = isset($request_data['page']) ? (int) $request_data['page'] : 1;
            $per_page = isset($request_data['per_page']) ? (int) $request_data['per_page'] : 25;
            $category = isset($request_data['category']) ? sanitize_text_field($request_data['category']) : '';
            $search = isset($request_data['search']) ? sanitize_text_field($request_data['search']) : '';
            
            $args = [
                'page' => $page,
                'per_page' => $per_page,
                'category' => $category,
                'search' => $search,
            ];
            
            $tests = $this->tdl_test_catalog->getTests($args);
            $total = $this->tdl_test_catalog->countTests([
                'category' => $category,
                'search' => $search,
            ]);
            
            $total_pages = ceil($total / $per_page);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Tests retrieved successfully', 'kc-lang'),
                'data' => $tests
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Create a new test request
     */
    public function createTestRequest() {
        try {
            $request_data = $this->request->getInputs();
            
            // Validate required fields
            $required_fields = ['patient_id', 'doctor_id', 'clinic_id', 'collection_date'];
            foreach ($required_fields as $field) {
                if (empty($request_data[$field])) {
                    wp_send_json([
                        'status' => false,
                        'message' => sprintf(esc_html__('%s is required', 'kc-lang'), ucfirst(str_replace('_', ' ', $field))),
                        'data' => []
                    ]);
                }
            }
            
            if (empty($request_data['tests']) || !is_array($request_data['tests'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('At least one test must be selected', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // Generate order number
            $order_number = $this->tdl_test_request->generateOrderNumber($request_data['clinic_id']);
            
            // Create request
            $request_data_to_save = [
                'patient_id' => (int) $request_data['patient_id'],
                'doctor_id' => (int) $request_data['doctor_id'],
                'clinic_id' => (int) $request_data['clinic_id'],
                'order_number' => $order_number,
                'status' => 'pending',
                'collection_date' => sanitize_text_field($request_data['collection_date']),
                'clinical_notes' => isset($request_data['clinical_notes']) ? sanitize_textarea_field($request_data['clinical_notes']) : '',
            ];
            
            // Generate HL7 message
            $hl7_message = $this->generateHL7Message($request_data_to_save, $request_data['tests']);
            $request_data_to_save['hl7_message'] = $hl7_message;
            
            $request_id = $this->tdl_test_request->createRequest($request_data_to_save);
            
            if (!$request_id) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to create test request', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // Add test items
            foreach ($request_data['tests'] as $test) {
                $item_data = [
                    'request_id' => $request_id,
                    'test_code' => sanitize_text_field($test['test_code']),
                    'test_name' => sanitize_text_field($test['test_name']),
                ];
                
                $this->tdl_test_request_item->addItem($item_data);
            }
            
            // Send HL7 message to TDL via Azure
            $this->sendHL7ToAzure($request_id, $hl7_message, $request_data['clinic_id']);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Test request created successfully', 'kc-lang'),
                'data' => [
                    'request_id' => $request_id,
                    'order_number' => $order_number,
                ]
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Generate HL7 message for TDL request
     *
     * @param array $request_data Request data
     * @param array $tests Tests to include
     * @return string HL7 message
     */
    private function generateHL7Message($request_data, $tests) {
        // Get patient, doctor, and clinic details

        $doctor = get_user_by('ID', $request_data['doctor_id']);
        $patient = get_user_by('ID', $request_data['patient_id']);
        $clinic = $this->clinic_model->get_by(['id' => $request_data['clinic_id']]);
        
        echo json_encode($clinic);exit;
        
        if (empty($patient) || empty($doctor) || empty($clinic)) {
            throw new Exception(esc_html__('Invalid patient, doctor, or clinic', 'kc-lang'));
        }
        
        $patient_meta = get_user_meta($patient->ID);
        $doctor_meta = get_user_meta($doctor->ID);
        
        // Get clinic TDL settings
        $tdl_settings = $this->tdl_clinic_setting->getClinicSettings($request_data['clinic_id']);
        
        if (empty($tdl_settings)) {
            throw new Exception(esc_html__('TDL settings not configured for this clinic', 'kc-lang'));
        }
        
        // Format patient DOB
        $dob = isset($patient_meta['basic_data'][0]) ? json_decode($patient_meta['basic_data'][0], true)['dob'] : '';
        $formatted_dob = !empty($dob) ? date('Ymd', strtotime($dob)) : '';
        
        // Build HL7 message
        $field_separator = '|';
        $component_separator = '^';
        $subcomponent_separator = '&';
        $repeat_separator = '~';
        $escape_char = '\\';
        $sending_application = 'KIVICARE';
        $sending_facility = 'KIVCARE_' . substr($clinic->name, 0, 5);  // Limit to 10 chars
        $receiving_application = 'TDL';
        $receiving_facility = 'TDL';
        $message_type = 'ORM';
        $message_event = 'O01';
        $processing_id = 'P';  // Production
        $version_id = '2.3';
        
        // MSH segment
        $datetime = date('YmdHis');
        $message_control_id = 'KC' . date('ymdHis') . rand(100, 999);
        
        $msh = "MSH{$field_separator}{$component_separator}{$subcomponent_separator}{$repeat_separator}{$escape_char}{$field_separator}";
        $msh .= "{$sending_application}{$field_separator}{$sending_facility}{$field_separator}{$receiving_application}{$field_separator}{$receiving_facility}{$field_separator}";
        $msh .= "{$datetime}{$field_separator}{$field_separator}{$message_type}{$component_separator}{$message_event}{$field_separator}";
        $msh .= "{$message_control_id}{$field_separator}{$processing_id}{$field_separator}{$version_id}";
        
        // PID segment
        $patient_id = substr($patient->id, 0, 10);  // Limit to 10 chars
        $lastname = substr($patient->last_name, 0, 20);
        $firstname = substr($patient->first_name, 0, 20);
        $gender = isset($patient_meta['gender'][0]) ? strtoupper($patient_meta['gender'][0]) : '';
        $gender = ($gender == 'MALE') ? 'M' : (($gender == 'FEMALE') ? 'F' : 'U');
        
        $pid = "PID{$field_separator}1{$field_separator}{$patient_id}{$field_separator}{$field_separator}{$field_separator}";
        $pid .= "{$lastname}{$component_separator}{$firstname}{$field_separator}{$field_separator}{$field_separator}";
        $pid .= "{$formatted_dob}{$field_separator}{$gender}";
        
        // PV1 segment
        $doctor_id = substr($doctor->ID, 0, 10);  // Limit to 10 chars
        $doctor_lastname = isset($doctor_meta['last_name'][0]) ? substr($doctor_meta['last_name'][0], 0, 20) : '';
        $doctor_firstname = isset($doctor_meta['first_name'][0]) ? substr($doctor_meta['first_name'][0], 0, 20) : '';
        
        $pv1 = "PV1{$field_separator}1{$field_separator}O{$field_separator}{$field_separator}{$field_separator}{$field_separator}{$field_separator}";
        $pv1 .= "{$doctor_id}{$component_separator}{$doctor_lastname}{$component_separator}{$doctor_firstname}";
        
        // ORC segments (one per test)
        $order_control = 'NW';  // New order
        $order_number = $request_data['order_number'];
        $order_date = date('YmdHis', strtotime($request_data['collection_date']));
        
        $orc_list = [];
        foreach ($tests as $index => $test) {
            $filler_number = $order_number . sprintf("%02d", $index + 1);
            $orc = "ORC{$field_separator}{$order_control}{$field_separator}{$order_number}{$field_separator}{$filler_number}{$field_separator}{$field_separator}";
            $orc .= "{$order_date}";
            $orc_list[] = $orc;
        }
        
        // OBR segments (one per test)
        $obr_list = [];
        foreach ($tests as $index => $test) {
            $sequence = $index + 1;
            $filler_number = $order_number . sprintf("%02d", $sequence);
            $test_code = $test['test_code'];
            $test_name = substr($test['test_name'], 0, 30);
            
            $obr = "OBR{$field_separator}{$sequence}{$field_separator}{$order_number}{$field_separator}{$filler_number}{$field_separator}";
            $obr .= "{$test_code}{$component_separator}{$test_name}{$field_separator}{$field_separator}{$field_separator}{$field_separator}";
            $obr .= "{$order_date}";
            $obr_list[] = $obr;
        }
        
        // Build complete message
        $message = $msh . "\r\n" . $pid . "\r\n" . $pv1 . "\r\n";
        
        for ($i = 0; $i < count($tests); $i++) {
            $message .= $orc_list[$i] . "\r\n" . $obr_list[$i] . "\r\n";
        }
        
        return $message;
    }

    /**
     * Send HL7 message to Azure Blob Storage
     *
     * @param int $request_id Request ID
     * @param string $hl7_message HL7 message
     * @param int $clinic_id Clinic ID
     * @return bool Success or failure
     * @throws Exception
     */
    private function sendHL7ToAzure($request_id, $hl7_message, $clinic_id) {
        // Get clinic TDL settings
        $tdl_settings = $this->tdl_clinic_setting->getClinicSettings($clinic_id);
        
        if (empty($tdl_settings) || empty($tdl_settings['azure_storage_connection']) || empty($tdl_settings['azure_storage_container'])) {
            throw new Exception(esc_html__('Azure storage settings not configured', 'kc-lang'));
        }
        
        // TODO: Implement Azure Blob Storage upload using the MicrosoftAzure PHP SDK
        // This would be implemented in a real-world scenario
        
        // Instead, update the request status to 'sent' to simulate success
        $this->tdl_test_request->updateRequest($request_id, ['status' => 'sent']);
        
        return true;
    }

    /**
     * Get test requests
     */
/**
 * Get test requests
 */
public function getTestRequests() {
    try {
        $request_data = $this->request->getInputs();
        
        $page = isset($request_data['page']) ? (int) $request_data['page'] : 1;
        $per_page = isset($request_data['per_page']) ? (int) $request_data['per_page'] : 25;
        $clinic_id = isset($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : null;
        $patient_id = isset($request_data['patient_id']) ? (int) $request_data['patient_id'] : null;
        $doctor_id = isset($request_data['doctor_id']) ? (int) $request_data['doctor_id'] : null;
        $status = isset($request_data['status']) ? sanitize_text_field($request_data['status']) : null;
        $date_from = isset($request_data['date_from']) ? sanitize_text_field($request_data['date_from']) : null;
        $date_to = isset($request_data['date_to']) ? sanitize_text_field($request_data['date_to']) : null;
        
        $args = [
            'page' => $page,
            'per_page' => $per_page,
            'clinic_id' => $clinic_id,
            'patient_id' => $patient_id,
            'doctor_id' => $doctor_id,
            'status' => $status,
            'date_from' => $date_from,
            'date_to' => $date_to,
        ];
        
        $requests = $this->tdl_test_request->getRequests($args);
        $total = $this->tdl_test_request->countRequests([
            'clinic_id' => $clinic_id,
            'patient_id' => $patient_id,
            'doctor_id' => $doctor_id,
            'status' => $status,
            'date_from' => $date_from,
            'date_to' => $date_to,
        ]);
        
        $total_pages = ceil($total / $per_page);
        
        // Get items for each request
        $requests_with_details = [];
        
        if (is_array($requests) && !empty($requests)) {
            foreach ($requests as $request) {
                // Skip if the request is not an array or doesn't have required fields
                if (!is_array($request) || !isset($request['id'])) {
                    continue;
                }
                
                $request_with_details = $request;
                
                // Get items for the request
                $request_with_details['items'] = $this->tdl_test_request_item->getItemsByRequestId($request['id']);
                
                // Get patient name if patient_id exists
                if (isset($request['patient_id']) && !empty($request['patient_id'])) {
                    $patient = $this->user_model->get_by(['id' => $request['patient_id']]);
                    $request_with_details['patient_name'] = ($patient && isset($patient->first_name) && isset($patient->last_name)) 
                        ? $patient->first_name . ' ' . $patient->last_name 
                        : '';
                } else {
                    $request_with_details['patient_name'] = '';
                }
                
                // Get doctor name if doctor_id exists
                if (isset($request['doctor_id']) && !empty($request['doctor_id'])) {
                    $doctor = $this->user_model->get_by(['id' => $request['doctor_id']]);
                    $request_with_details['doctor_name'] = ($doctor && isset($doctor->display_name)) 
                        ? $doctor->display_name 
                        : '';
                } else {
                    $request_with_details['doctor_name'] = '';
                }
                
                $requests_with_details[] = $request_with_details;
            }
        }
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Requests retrieved successfully', 'kc-lang'),
            'data' => [
                'requests' => $requests_with_details,
                'total' => $total,
                'total_pages' => $total_pages,
                'current_page' => $page,
            ]
        ]);
    } catch (Exception $e) {
        wp_send_json([
            'status' => false,
            'message' => $e->getMessage(),
            'data' => []
        ]);
    }
}

    /**
     * Get test request details
     */
    public function getTestRequestDetails() {
        try {
            $request_data = $this->request->getInputs();
            $request_id = isset($request_data['id']) ? (int) $request_data['id'] : 0;
            
            if (empty($request_id)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Request ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            $request_data = $this->tdl_test_request->getRequestById($request_id);
            
            if (empty($request_data)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Request not found', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // Get test items
            $request_data['items'] = $this->tdl_test_request_item->getItemsByRequestId($request_id);
            
            // Get patient and doctor details
            $patient = $this->user_model->get_by(['id' => $request_data['patient_id']]);
            $doctor = $this->user_model->get_by(['id' => $request_data['doctor_id']]);
            $clinic = $this->clinic_model->get_by(['id' => $request_data['clinic_id']]);
            
            $request_data['patient'] = $patient ? [
                'id' => $patient->id,
                'name' => $patient->first_name . ' ' . $patient->last_name,
            ] : null;
            
            $request_data['doctor'] = $doctor ? [
                'id' => $doctor->ID,
                'name' => $doctor->display_name,
            ] : null;
            
            $request_data['clinic'] = $clinic ? [
                'id' => $clinic->id,
                'name' => $clinic->name,
            ] : null;
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Request details retrieved successfully', 'kc-lang'),
                'data' => $request_data
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Get test results
     */
    public function getTestResults() {
        try {
            $request_data = $this->request->getInputs();
            
            $page = isset($request_data['page']) ? (int) $request_data['page'] : 1;
            $per_page = isset($request_data['per_page']) ? (int) $request_data['per_page'] : 25;
            $clinic_id = isset($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : null;
            $patient_id = isset($request_data['patient_id']) ? (int) $request_data['patient_id'] : null;
            $doctor_id = isset($request_data['doctor_id']) ? (int) $request_data['doctor_id'] : null;
            $request_id = isset($request_data['request_id']) ? (int) $request_data['request_id'] : null;
            $result_status = isset($request_data['result_status']) ? sanitize_text_field($request_data['result_status']) : null;
            $date_from = isset($request_data['date_from']) ? sanitize_text_field($request_data['date_from']) : null;
            $date_to = isset($request_data['date_to']) ? sanitize_text_field($request_data['date_to']) : null;
            
            $args = [
                'page' => $page,
                'per_page' => $per_page,
                'clinic_id' => $clinic_id,
                'patient_id' => $patient_id,
                'doctor_id' => $doctor_id,
                'request_id' => $request_id,
                'result_status' => $result_status,
                'date_from' => $date_from,
                'date_to' => $date_to,
            ];
            
            $results = $this->tdl_test_result->getResults($args);
            $total = $this->tdl_test_result->countResults([
                'clinic_id' => $clinic_id,
                'patient_id' => $patient_id,
                'doctor_id' => $doctor_id,
                'request_id' => $request_id,
                'result_status' => $result_status,
                'date_from' => $date_from,
                'date_to' => $date_to,
            ]);
            
            $total_pages = ceil($total / $per_page);
            
            // Get patient and doctor names for each result
            foreach ($results as &$result) {
                $patient = $this->user_model->get_by(['id' => $result['patient_id']]);
                $doctor = isset($result['doctor_id']) && $result['doctor_id'] ? $this->user_model->get_by(['id' => $result['doctor_id']]) : null;
                
                $result['patient_name'] = $patient ? $patient->first_name . ' ' . $patient->last_name : '';
                $result['doctor_name'] = $doctor ? $doctor->display_name : '';
                
                // Get tests count
                $items = $this->tdl_test_result_item->getItemsByResultId($result['id']);
                $test_codes = [];
                foreach ($items as $item) {
                    if (!in_array($item['test_code'], $test_codes)) {
                        $test_codes[] = $item['test_code'];
                    }
                }
                
                $result['tests_count'] = count($test_codes);
                $result['biomarkers_count'] = count($items);
                
                // Clear result_data JSON from response to save bandwidth
                unset($result['result_data']);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Results retrieved successfully', 'kc-lang'),
                'data' => [
                    'results' => $results,
                    'total' => $total,
                    'total_pages' => $total_pages,
                    'current_page' => $page,
                ]
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * Get test result details
     */
    public function getTestResultDetails() {
        try {
            $request_data = $this->request->getInputs();
            $result_id = isset($request_data['id']) ? (int) $request_data['id'] : 0;
            
            if (empty($result_id)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Result ID is required', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            $result_data = $this->tdl_test_result->getResultById($result_id);
            
            if (empty($result_data)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Result not found', 'kc-lang'),
                    'data' => []
                ]);
            }
            
            // Get test result items grouped by test
            $result_data['tests'] = $this->tdl_test_result_item->getTestsGroupedByCode($result_id);
            
            // Get patient and doctor details
            $patient = $this->user_model->get_by(['id' => $result_data['patient_id']]);
            $doctor = isset($result_data['doctor_id']) && $result_data['doctor_id'] ? $this->user_model->get_by(['id' => $result_data['doctor_id']]) : null;
            $clinic = $this->clinic_model->get_by(['id' => $result_data['clinic_id']]);
            
            $result_data['patient'] = $patient ? [
                'id' => $patient->id,
                'name' => $patient->first_name . ' ' . $patient->last_name,
            ] : null;
            
            $result_data['doctor'] = $doctor ? [
                'id' => $doctor->ID,
                'name' => $doctor->display_name,
            ] : null;
            
            $result_data['clinic'] = $clinic ? [
                'id' => $clinic->id,
                'name' => $clinic->name,
            ] : null;
            
            // Get reviewer details if reviewed
            if (!empty($result_data['reviewed_by'])) {
                $reviewer = $this->user_model->get_by(['id' => $result_data['reviewed_by']]);
                $result_data['reviewer'] = $reviewer ? [
                    'id' => $reviewer->ID,
                    'name' => $reviewer->display_name,
                ] : null;
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Result details retrieved successfully', 'kc-lang'),
                'data' => $result_data
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }
}