<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTDLTestCatalog extends KCModel {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct('tdl_test_catalog');
    }

    /**
     * Get all tests in the catalog
     *
     * @param array $args Optional arguments for pagination and filtering
     * @return array Test catalog entries
     */
    public function getTests($args = []) {
        global $wpdb;
        
        $defaults = [
            'page' => 1,
            'per_page' => 25,
            'category' => '',
            'search' => '',
            'orderby' => 'test_name',
            'order' => 'ASC'
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['category'])) {
            $where .= ' AND test_category = %s';
            $params[] = sanitize_text_field($args['category']);
        }
        
        if (!empty($args['search'])) {
            $where .= ' AND (test_code LIKE %s OR test_name LIKE %s OR test_description LIKE %s)';
            $search_term = '%' . $wpdb->esc_like(sanitize_text_field($args['search'])) . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        // Add test code filtering if provided
        if (!empty($args['test_code'])) {
            $where .= ' AND test_code = %s';
            $params[] = sanitize_text_field($args['test_code']);
        }
        
        // Add test group filtering if provided
        if (!empty($args['test_group'])) {
            $where .= ' AND test_group = %s';
            $params[] = sanitize_text_field($args['test_group']);
        }
        
        // Handle ordering
        $orderby = sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']) ?: 'test_name ASC';
        
        // First get the total count for pagination info
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        $total = (int) $wpdb->get_var($wpdb->prepare($count_query, $params));
        
        // Now get the actual results with pagination
        if ($args['per_page'] > 0) {
            $limit = (int) $args['per_page'];
            $offset = (int) (($args['page'] - 1) * $limit);
            $limit_clause = "LIMIT %d OFFSET %d";
            $params[] = $limit;
            $params[] = $offset;
        } else {
            $limit_clause = "";
        }
        
        $query = "SELECT * FROM {$table_name} WHERE {$where} ORDER BY {$orderby} {$limit_clause}";
        $query = $wpdb->prepare($query, $params);
        $results = $wpdb->get_results($query, ARRAY_A);
        
        // Return both the results and pagination info if per_page is set
        if (isset($args['per_page']) && $args['per_page'] > 0) {
            return [
                'tests' => $results,
                'total' => $total,
                'page' => (int) $args['page'],
                'per_page' => (int) $args['per_page'],
                'total_pages' => ceil($total / $args['per_page'])
            ];
        }
        
        return $results;
    }

    /**
     * Count total tests with optional filtering
     *
     * @param array $args Optional filter arguments
     * @return int Number of tests
     */
    public function countTests($args = []) {
        global $wpdb;
        
        $defaults = [
            'category' => '',
            'search' => '',
            'test_code' => '',
            'test_group' => '',
        ];
        
        $args = wp_parse_args($args, $defaults);
        $table_name = $this->get_table_name();
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['category'])) {
            $where .= ' AND test_category = %s';
            $params[] = sanitize_text_field($args['category']);
        }
        
        if (!empty($args['search'])) {
            $where .= ' AND (test_code LIKE %s OR test_name LIKE %s OR test_description LIKE %s)';
            $search_term = '%' . $wpdb->esc_like(sanitize_text_field($args['search'])) . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        if (!empty($args['test_code'])) {
            $where .= ' AND test_code = %s';
            $params[] = sanitize_text_field($args['test_code']);
        }
        
        if (!empty($args['test_group'])) {
            $where .= ' AND test_group = %s';
            $params[] = sanitize_text_field($args['test_group']);
        }
        
        $query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where}";
        
        return (int) $wpdb->get_var($wpdb->prepare($query, $params));
    }

    /**
     * Get a test by its code
     *
     * @param string $test_code The test code
     * @return array|false Test data or false if not found
     */
    public function getTestByCode($test_code) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $test_code = sanitize_text_field($test_code);
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE test_code = %s", 
            $test_code
        );
        
        return $wpdb->get_row($query, ARRAY_A);
    }

    /**
     * Get distinct test categories
     *
     * @return array List of unique test categories
     */
    public function getTestCategories() {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $query = "SELECT DISTINCT test_category FROM {$table_name} ORDER BY test_category ASC";
        
        return $wpdb->get_col($query);
    }

    /**
     * Get distinct test groups
     *
     * @return array List of unique test groups
     */
    public function getTestGroups() {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $query = "SELECT DISTINCT test_group FROM {$table_name} WHERE test_group != '' ORDER BY test_group ASC";
        
        return $wpdb->get_col($query);
    }

    /**
     * Add or update a test in the catalog
     *
     * @param array $test_data Test data
     * @return int|false ID of inserted/updated record or false on failure
     */
    public function saveTest($test_data) {
        if (empty($test_data['test_code'])) {
            return false;
        }
        
        $test_code = sanitize_text_field($test_data['test_code']);
        $existing_test = $this->getTestByCode($test_code);
        
        // Sanitize data
        $save_data = [
            'test_code' => $test_code,
            'test_name' => isset($test_data['test_name']) ? sanitize_text_field($test_data['test_name']) : '',
            'test_category' => isset($test_data['test_category']) ? sanitize_text_field($test_data['test_category']) : '',
            'test_group' => isset($test_data['test_group']) ? sanitize_text_field($test_data['test_group']) : '',
            'last_updated' => current_time('mysql')
        ];
        
        // Optional fields
        if (isset($test_data['test_description'])) {
            $save_data['test_description'] = sanitize_textarea_field($test_data['test_description']);
        }
        
        if (isset($test_data['price'])) {
            $save_data['price'] = (float) $test_data['price'];
        }
        
        if (isset($test_data['turnaround_time'])) {
            $save_data['turnaround_time'] = sanitize_text_field($test_data['turnaround_time']);
        }
        
        if (isset($test_data['sample_type'])) {
            $save_data['sample_type'] = sanitize_text_field($test_data['sample_type']);
        }
        
        if (isset($test_data['is_active'])) {
            $save_data['is_active'] = (int) $test_data['is_active'];
        }
        
        if (isset($test_data['meta_data']) && is_array($test_data['meta_data'])) {
            $save_data['meta_data'] = wp_json_encode($test_data['meta_data']);
        } else if (isset($test_data['meta_data']) && is_string($test_data['meta_data'])) {
            $save_data['meta_data'] = sanitize_text_field($test_data['meta_data']);
        }
        
        if ($existing_test) {
            // Update existing test
            return $this->update($save_data, ['test_code' => $test_code]);
        } else {
            // Insert new test
            $save_data['created_at'] = current_time('mysql');
            return $this->insert($save_data);
        }
    }

    /**
     * Delete a test from the catalog
     *
     * @param string $test_code The test code to delete
     * @return bool Success or failure
     */
    public function deleteTest($test_code) {
        if (empty($test_code)) {
            return false;
        }
        
        return $this->delete(['test_code' => sanitize_text_field($test_code)]);
    }

    /**
     * Bulk import tests into the catalog
     *
     * @param array $tests Array of test data
     * @return array Results with counts of imported and failed tests
     */
    public function bulkImportTests($tests) {
        if (!is_array($tests) || empty($tests)) {
            return [
                'imported' => 0,
                'failed' => 0,
                'errors' => ['No valid test data provided']
            ];
        }
        
        $imported = 0;
        $failed = 0;
        $errors = [];
        
        foreach ($tests as $test) {
            if (empty($test['test_code']) || empty($test['test_name'])) {
                $failed++;
                $errors[] = 'Test missing required fields: code and name';
                continue;
            }
            
            $result = $this->saveTest($test);
            
            if ($result) {
                $imported++;
            } else {
                $failed++;
                $errors[] = 'Failed to import test: ' . $test['test_code'];
            }
        }
        
        return [
            'imported' => $imported,
            'failed' => $failed,
            'errors' => $errors
        ];
    }
}