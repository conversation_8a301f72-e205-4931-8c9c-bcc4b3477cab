<?php
/**
 * Migration: Create billing payment mapping table
 * File: create_billing_payment_mapping_table.php
 */
if (!defined('ABSPATH')) {
    exit;
}

class CreateBillingPaymentMappingTable {
    /**
     * Run the migration - creates the kc_billing_payment_mapping table
     */
    public function up() {
        global $wpdb;
        error_log("[Migration] Creating kc_billing_payment_mapping table");
        
        $table = $wpdb->prefix . 'kc_billing_payment_mapping';
        $charset_collate = $wpdb->get_charset_collate();
        
        // Check if table already exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if ($table_exists) {
            error_log("[Migration] Table kc_billing_payment_mapping already exists, skipping creation.");
            return true;
        }
        
        // Create the table
        $sql = "CREATE TABLE {$table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            billing_id bigint(20) NOT NULL,
            payment_id bigint(20) NOT NULL,
            amount decimal(10,2) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY billing_id (billing_id),
            KEY payment_id (payment_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error creating table: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Table kc_billing_payment_mapping created successfully.");
        return true;
    }
    
    /**
     * Reverse the migration - drops the kc_billing_payment_mapping table
     */
    public function down() {
        global $wpdb;
        error_log("[Migration] Dropping kc_billing_payment_mapping table");
        
        $table = $wpdb->prefix . 'kc_billing_payment_mapping';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            error_log("[Migration] Table kc_billing_payment_mapping doesn't exist, skipping drop.");
            return true;
        }
        
        // Drop the table
        $sql = "DROP TABLE IF EXISTS {$table}";
        $wpdb->query($sql);
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error dropping table: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Table kc_billing_payment_mapping dropped successfully.");
        return true;
    }
}