<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTDLTestRequestItem extends KCModel {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct('tdl_test_request_items');
    }

    /**
     * Get all test items for a request
     *
     * @param int $request_id Request ID
     * @return array Test items
     */
    public function getItemsByRequestId($request_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $request_id = (int)$request_id;
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE request_id = %d ORDER BY id ASC", 
            $request_id
        );
        
        return $wpdb->get_results($query, ARRAY_A);
    }

    /**
     * Get a specific test item by ID
     *
     * @param int $item_id Item ID
     * @return array|false Item data or false if not found
     */
    public function getItemById($item_id) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $item_id = (int)$item_id;
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d", 
            $item_id
        );
        
        return $wpdb->get_row($query, ARRAY_A);
    }

    /**
     * Add a test item to a request
     *
     * @param array $item_data Item data
     * @return int|false ID of new item or false on failure
     */
    public function addItem($item_data) {
        if (empty($item_data['request_id']) || empty($item_data['test_code']) || empty($item_data['test_name'])) {
            return false;
        }
        
        // Sanitize data
        $data_to_save = [
            'request_id' => (int)$item_data['request_id'],
            'test_code' => sanitize_text_field($item_data['test_code']),
            'test_name' => sanitize_text_field($item_data['test_name']),
            'created_at' => current_time('mysql')
        ];
        
        // Optional fields
        if (isset($item_data['test_description'])) {
            $data_to_save['test_description'] = sanitize_textarea_field($item_data['test_description']);
        }
        
        if (isset($item_data['sample_type'])) {
            $data_to_save['sample_type'] = sanitize_text_field($item_data['sample_type']);
        }
        
        if (isset($item_data['price'])) {
            $data_to_save['price'] = (float)$item_data['price'];
        }
        
        if (isset($item_data['meta_data']) && is_array($item_data['meta_data'])) {
            $data_to_save['meta_data'] = wp_json_encode($item_data['meta_data']);
        } else if (isset($item_data['meta_data']) && is_string($item_data['meta_data'])) {
            $data_to_save['meta_data'] = sanitize_text_field($item_data['meta_data']);
        }
        
        return $this->insert($data_to_save);
    }

    /**
     * Update a test item
     *
     * @param int $item_id Item ID
     * @param array $item_data Item data to update
     * @return bool Success or failure
     */
    public function updateItem($item_id, $item_data) {
        $item_id = (int)$item_id;
        
        if (empty($item_id)) {
            return false;
        }
        
        // Check if item exists
        $existing = $this->getItemById($item_id);
        if (!$existing) {
            return false;
        }
        
        // Prepare data to update
        $data_to_update = ['updated_at' => current_time('mysql')];
        
        // Only include fields that are provided
        if (isset($item_data['test_name'])) {
            $data_to_update['test_name'] = sanitize_text_field($item_data['test_name']);
        }
        
        if (isset($item_data['test_description'])) {
            $data_to_update['test_description'] = sanitize_textarea_field($item_data['test_description']);
        }
        
        if (isset($item_data['sample_type'])) {
            $data_to_update['sample_type'] = sanitize_text_field($item_data['sample_type']);
        }
        
        if (isset($item_data['price'])) {
            $data_to_update['price'] = (float)$item_data['price'];
        }
        
        if (isset($item_data['meta_data']) && is_array($item_data['meta_data'])) {
            $data_to_update['meta_data'] = wp_json_encode($item_data['meta_data']);
        } else if (isset($item_data['meta_data']) && is_string($item_data['meta_data'])) {
            $data_to_update['meta_data'] = sanitize_text_field($item_data['meta_data']);
        }
        
        return $this->update($data_to_update, ['id' => $item_id]);
    }

    /**
     * Delete all items for a request
     *
     * @param int $request_id Request ID
     * @return bool Success or failure
     */
    public function deleteItemsByRequestId($request_id) {
        $request_id = (int)$request_id;
        
        if (empty($request_id)) {
            return false;
        }
        
        return $this->delete(['request_id' => $request_id]);
    }

    /**
     * Delete a specific test item
     *
     * @param int $item_id Item ID
     * @return bool Success or failure
     */
    public function deleteItem($item_id) {
        $item_id = (int)$item_id;
        
        if (empty($item_id)) {
            return false;
        }
        
        return $this->delete(['id' => $item_id]);
    }
    
    /**
     * Get statistics on test items
     *
     * @param array $args Optional filter arguments
     * @return array Statistics data
     */
    public function getItemsStats($args = []) {
        global $wpdb;
        
        $defaults = [
            'clinic_id' => null,
            'date_from' => null,
            'date_to' => null,
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $table_name = $this->get_table_name();
        $requests_table = $wpdb->prefix . 'kc_tdl_test_requests';
        
        $where = '1=1';
        $params = [];
        
        if (!empty($args['clinic_id'])) {
            $where .= ' AND r.clinic_id = %d';
            $params[] = (int)$args['clinic_id'];
        }
        
        if (!empty($args['date_from'])) {
            $where .= ' AND r.created_at >= %s';
            $params[] = sanitize_text_field($args['date_from']);
        }
        
        if (!empty($args['date_to'])) {
            $where .= ' AND r.created_at <= %s';
            $params[] = sanitize_text_field($args['date_to']);
        }
        
        // Get most requested tests
        $top_tests_query = "
            SELECT i.test_code, i.test_name, COUNT(*) as count
            FROM {$table_name} i
            JOIN {$requests_table} r ON i.request_id = r.id
            WHERE {$where}
            GROUP BY i.test_code, i.test_name
            ORDER BY count DESC
            LIMIT 10
        ";
        
        $top_tests = $wpdb->get_results($wpdb->prepare($top_tests_query, $params), ARRAY_A);
        
        // Get total number of test items
        $count_query = "
            SELECT COUNT(*) 
            FROM {$table_name} i
            JOIN {$requests_table} r ON i.request_id = r.id
            WHERE {$where}
        ";
        
        $total_items = (int)$wpdb->get_var($wpdb->prepare($count_query, $params));
        
        // Get unique test count
        $unique_query = "
            SELECT COUNT(DISTINCT i.test_code) 
            FROM {$table_name} i
            JOIN {$requests_table} r ON i.request_id = r.id
            WHERE {$where}
        ";
        
        $unique_tests = (int)$wpdb->get_var($wpdb->prepare($unique_query, $params));
        
        return [
            'total_items' => $total_items,
            'unique_tests' => $unique_tests,
            'top_tests' => $top_tests
        ];
    }
    
    /**
     * Bulk add test items to a request
     *
     * @param int $request_id Request ID
     * @param array $tests Array of test data
     * @return array Results with counts of added and failed items
     */
    public function bulkAddItems($request_id, $tests) {
        $request_id = (int)$request_id;
        
        if (empty($request_id) || !is_array($tests) || empty($tests)) {
            return [
                'added' => 0,
                'failed' => 0
            ];
        }
        
        $added = 0;
        $failed = 0;
        
        foreach ($tests as $test) {
            // Make sure test has request_id set
            $test['request_id'] = $request_id;
            
            $result = $this->addItem($test);
            
            if ($result) {
                $added++;
            } else {
                $failed++;
            }
        }
        
        return [
            'added' => $added,
            'failed' => $failed
        ];
    }
}