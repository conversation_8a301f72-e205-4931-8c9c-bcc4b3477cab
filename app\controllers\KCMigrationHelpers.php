<?php

namespace App\controllers;

trait KCMigrationHelpers
{
    /**
     * Validate clinic access for current user
     */
    private function validateClinicAccess($clinic_id)
    {
        $current_user = wp_get_current_user();
        
        // Administrators have access to all clinics
        if (in_array('administrator', $current_user->roles)) {
            return true;
        }
        
        // Clinic admins have access to their clinic
        if (in_array($this->getClinicAdminRole(), $current_user->roles)) {
            $user_clinic_id = kcGetClinicIdOfClinicAdmin();
            return $user_clinic_id == $clinic_id;
        }
        
        return false;
    }

    /**
     * Format clinic data for API response
     */
    private function formatClinicData($clinic)
    {
        return array(
            'id' => (int) $clinic->id,
            'name' => $clinic->name,
            'email' => $clinic->email,
            'telephone_no' => $clinic->telephone_no,
            'address' => $clinic->address,
            'city' => $clinic->city,
            'state' => $clinic->state,
            'country' => $clinic->country,
            'postal_code' => $clinic->postal_code,
            'specialties' => $clinic->specialties ? json_decode($clinic->specialties, true) : array(),
            'status' => (int) $clinic->status,
            'clinic_admin_id' => (int) $clinic->clinic_admin_id,
            'extra' => $clinic->extra ? json_decode($clinic->extra, true) : array(),
            'created_at' => $clinic->created_at,
        );
    }

    /**
     * Get doctors by clinic ID
     */
    private function getDoctorsByClinic($clinic_id)
    {
        $doctors = $this->db->get_results($this->db->prepare("
            SELECT u.*, dcm.clinic_id, dcm.created_at as mapping_created_at
            FROM {$this->db->prefix}users u
            INNER JOIN {$this->db->prefix}kc_doctor_clinic_mappings dcm ON u.ID = dcm.doctor_id
            WHERE dcm.clinic_id = %d
        ", $clinic_id));
        
        $formatted_doctors = array();
        foreach ($doctors as $doctor) {
            $formatted_doctors[] = $this->formatUserData($doctor, 'kiviCare_doctor');
        }
        
        return $formatted_doctors;
    }

    /**
     * Get patients by clinic ID
     */
    private function getPatientsByClinic($clinic_id)
    {
        $patients = $this->db->get_results($this->db->prepare("
            SELECT u.*, pcm.clinic_id, pcm.created_at as mapping_created_at
            FROM {$this->db->prefix}users u
            INNER JOIN {$this->db->prefix}kc_patient_clinic_mappings pcm ON u.ID = pcm.patient_id
            WHERE pcm.clinic_id = %d
        ", $clinic_id));
        
        $formatted_patients = array();
        foreach ($patients as $patient) {
            $formatted_patients[] = $this->formatUserData($patient, 'kiviCare_patient');
        }
        
        return $formatted_patients;
    }

    /**
     * Get receptionists by clinic ID
     */
    private function getReceptionistsByClinic($clinic_id)
    {
        $receptionists = $this->db->get_results($this->db->prepare("
            SELECT u.*, rcm.clinic_id, rcm.created_at as mapping_created_at
            FROM {$this->db->prefix}users u
            INNER JOIN {$this->db->prefix}kc_receptionist_clinic_mappings rcm ON u.ID = rcm.receptionist_id
            WHERE rcm.clinic_id = %d
        ", $clinic_id));
        
        $formatted_receptionists = array();
        foreach ($receptionists as $receptionist) {
            $formatted_receptionists[] = $this->formatUserData($receptionist, 'kiviCare_receptionist');
        }
        
        return $formatted_receptionists;
    }

    /**
     * Get clinic admin
     */
    private function getClinicAdmin($clinic_id)
    {
        $clinic = $this->db->get_row($this->db->prepare("
            SELECT * FROM {$this->db->prefix}kc_clinics WHERE id = %d
        ", $clinic_id));
        
        if ($clinic && $clinic->clinic_admin_id) {
            $admin = get_user_by('ID', $clinic->clinic_admin_id);
            if ($admin) {
                return $this->formatUserData($admin, 'kiviCare_clinic_admin');
            }
        }
        
        return null;
    }

    /**
     * Format user data for API response
     */
    private function formatUserData($user, $role = null)
    {
        // Get user meta
        $meta = array(
            'first_name' => get_user_meta($user->ID, 'first_name', true),
            'last_name' => get_user_meta($user->ID, 'last_name', true),
            'mobile_number' => get_user_meta($user->ID, 'mobile_number', true),
            'gender' => get_user_meta($user->ID, 'gender', true),
            'dob' => get_user_meta($user->ID, 'dob', true),
        );
        
        // Get user roles
        $user_obj = get_user_by('ID', $user->ID);
        $roles = $user_obj ? $user_obj->roles : array();
        
        // If specific role provided, use it
        if ($role) {
            $roles = array($role);
        }
        
        $formatted_user = array(
            'ID' => (int) $user->ID,
            'user_login' => $user->user_login,
            'user_email' => $user->user_email,
            'display_name' => $user->display_name,
            'user_registered' => $user->user_registered,
            'roles' => $roles,
            'meta' => $meta,
        );
        
        // Add clinic mapping data if available
        if (isset($user->clinic_id)) {
            $formatted_user['clinic_mapping'] = array(
                'clinic_id' => (int) $user->clinic_id,
                'created_at' => $user->mapping_created_at ?? null,
            );
        }
        
        return $formatted_user;
    }

    /**
     * Format service data for API response
     */
    private function formatServiceData($service)
    {
        // Get service-doctor mappings
        $doctor_mappings = $this->db->get_results($this->db->prepare("
            SELECT * FROM {$this->db->prefix}kc_service_doctor_mapping 
            WHERE service_id = %d
        ", $service->id));
        
        $formatted_mappings = array();
        foreach ($doctor_mappings as $mapping) {
            $formatted_mappings[] = array(
                'doctor_id' => (int) $mapping->doctor_id,
                'charges' => $mapping->charges,
            );
        }
        
        return array(
            'id' => (int) $service->id,
            'name' => $service->name,
            'type' => $service->type,
            'price' => $service->price,
            'duration' => $service->duration,
            'status' => (int) $service->status,
            'clinic_id' => (int) $service->clinic_id,
            'doctor_mappings' => $formatted_mappings,
            'created_at' => $service->created_at,
        );
    }

    /**
     * Format appointment data for API response
     */
    private function formatAppointmentData($appointment)
    {
        // Get appointment service mappings
        $service_mappings = $this->db->get_results($this->db->prepare("
            SELECT * FROM {$this->db->prefix}kc_appointment_service_mapping 
            WHERE appointment_id = %d
        ", $appointment->id));
        
        $formatted_mappings = array();
        foreach ($service_mappings as $mapping) {
            $formatted_mappings[] = array(
                'service_id' => (int) $mapping->service_id,
                'price' => $mapping->price,
            );
        }
        
        return array(
            'id' => (int) $appointment->id,
            'appointment_start_date' => $appointment->appointment_start_date,
            'appointment_start_time' => $appointment->appointment_start_time,
            'appointment_end_date' => $appointment->appointment_end_date,
            'appointment_end_time' => $appointment->appointment_end_time,
            'visit_type' => $appointment->visit_type,
            'clinic_id' => (int) $appointment->clinic_id,
            'doctor_id' => (int) $appointment->doctor_id,
            'patient_id' => (int) $appointment->patient_id,
            'description' => $appointment->description,
            'status' => (int) $appointment->status,
            'service_mappings' => $formatted_mappings,
            'created_at' => $appointment->created_at,
        );
    }

    /**
     * Format encounter data for API response
     */
    private function formatEncounterData($encounter)
    {
        // Get encounter tabs/templates
        $tabs = $this->db->get_results($this->db->prepare("
            SELECT * FROM {$this->db->prefix}kc_encounter_tabs 
            WHERE encounter_id = %d
        ", $encounter->id));
        
        $formatted_tabs = array();
        foreach ($tabs as $tab) {
            $formatted_tabs[] = array(
                'tab_name' => $tab->tab_name,
                'content' => $tab->content,
            );
        }
        
        // Get encounter vitals
        $vitals = $this->db->get_results($this->db->prepare("
            SELECT * FROM {$this->db->prefix}kc_encounter_vitals 
            WHERE encounter_id = %d
        ", $encounter->id));
        
        $formatted_vitals = array();
        foreach ($vitals as $vital) {
            $formatted_vitals[$vital->vital_name] = $vital->vital_value;
        }
        
        return array(
            'id' => (int) $encounter->id,
            'encounter_date' => $encounter->encounter_date,
            'clinic_id' => (int) $encounter->clinic_id,
            'doctor_id' => (int) $encounter->doctor_id,
            'patient_id' => (int) $encounter->patient_id,
            'appointment_id' => (int) $encounter->appointment_id,
            'description' => $encounter->description,
            'status' => (int) $encounter->status,
            'added_by' => (int) $encounter->added_by,
            'template_id' => $encounter->template_id,
            'tabs' => $formatted_tabs,
            'vitals' => $formatted_vitals,
            'created_at' => $encounter->created_at,
        );
    }

    /**
     * Format prescription data for API response
     */
    private function formatPrescriptionData($prescription)
    {
        return array(
            'id' => (int) $prescription->id,
            'encounter_id' => (int) $prescription->encounter_id,
            'patient_id' => (int) $prescription->patient_id,
            'name' => $prescription->name,
            'frequency' => $prescription->frequency,
            'duration' => $prescription->duration,
            'instruction' => $prescription->instruction,
            'added_by' => (int) $prescription->added_by,
            'created_at' => $prescription->created_at,
        );
    }

    /**
     * Format bill data for API response
     */
    private function formatBillData($bill)
    {
        // Get bill items
        $bill_items = $this->db->get_results($this->db->prepare("
            SELECT * FROM {$this->db->prefix}kc_bill_items 
            WHERE bill_id = %d
        ", $bill->id));
        
        $formatted_items = array();
        foreach ($bill_items as $item) {
            $formatted_items[] = array(
                'item_id' => (int) $item->id,
                'service_id' => (int) $item->service_id,
                'price' => $item->price,
                'qty' => (int) $item->qty,
            );
        }
        
        return array(
            'id' => (int) $bill->id,
            'encounter_id' => (int) $bill->encounter_id,
            'appointment_id' => (int) $bill->appointment_id,
            'title' => $bill->title,
            'total_amount' => $bill->total_amount,
            'discount' => $bill->discount,
            'actual_amount' => $bill->actual_amount,
            'status' => (int) $bill->status,
            'payment_status' => $bill->payment_status,
            'items' => $formatted_items,
            'created_at' => $bill->created_at,
        );
    }
}
