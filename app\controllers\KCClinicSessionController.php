<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCClinicSession;

class KCClinicSessionController extends KCBase
{
    public $db;
    private $request;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        parent::__construct();
    }

    public function index() {
        if (!kcCheckPermission('doctor_session_list')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
    
        $request_data = $this->request->getInputs();
        $clinic_sessios_table = $this->db->prefix . 'kc_clinic_sessions';
        $user_table = $this->db->base_prefix . 'users';
        $clinic_table = $this->db->prefix . 'kc_clinics';
    
        // Check if Pro is active and use its filters if available
        if (isKiviCareProActive()) {
            $response = apply_filters('kcpro_clinic_session_list', []);
            if (empty($response['status']) && empty($response['data'])) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('No clinic session list found', 'kc-lang'),
                    'data' => [
                        'clinic_sessions' => []
                    ]
                ]);
            } else {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Clinic session list', 'kc-lang'),
                    'data' => [
                        'clinic_sessions' => $response['data']
                    ]
                ]);
            }
        } else {
            $query = "SELECT {$clinic_sessios_table}.*,{$user_table}.display_name AS doctor_name,{$clinic_table}.name AS clinic_name FROM {$clinic_sessios_table}
                    LEFT JOIN {$user_table} ON {$user_table}.ID = {$clinic_sessios_table}.doctor_id
                    LEFT JOIN {$clinic_table} ON {$clinic_table}.id = {$clinic_sessios_table}.clinic_id ";
            
            // Base conditions
            $where_conditions = " AND {$user_table}.user_status=0 ";
            
            // Role-based conditions
            if ($this->getLoginUserRole() === $this->getDoctorRole()) {
                $where_conditions .= " AND {$clinic_sessios_table}.doctor_id=" . get_current_user_id();
            } else {
                $where_conditions .= " AND {$clinic_sessios_table}.clinic_id=" . kcGetDefaultClinicId();
            }
            
            // Add service_id filter if provided
            if (!empty($request_data['service_id'])) {
                $service_id = (int)$request_data['service_id'];
                $where_conditions .= " AND {$clinic_sessios_table}.service_id={$service_id}";
            }
    
            // Get sessions with the applied conditions
            $clinic_sessions = $this->db->get_results("{$query} WHERE 0=0 {$where_conditions}");
    
            if (empty($clinic_sessions)) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('No clinic session list found', 'kc-lang'),
                    'data' => [
                        'clinic_sessions' => []
                    ]
                ]);
            }
    
            $clinic_sessions = kcClinicSession($clinic_sessions);
    
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Clinic session list', 'kc-lang'),
                'data' => [
                    'clinic_sessions' => $clinic_sessions
                ]
            ]);
        }
    }

    public function indexByService() {
        if (!kcCheckPermission('doctor_session_list')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
    
        $request_data = $this->request->getInputs();
        $clinic_sessios_table = $this->db->prefix . 'kc_clinic_sessions';
        $user_table = $this->db->base_prefix . 'users';
        $clinic_table = $this->db->prefix . 'kc_clinics';
    
        // Debug the incoming request data
        error_log("indexByService - Request data: " . print_r($request_data, true));
    

            $query = "SELECT {$clinic_sessios_table}.*,{$user_table}.display_name AS doctor_name,{$clinic_table}.name AS clinic_name FROM {$clinic_sessios_table}
                    LEFT JOIN {$user_table} ON {$user_table}.ID = {$clinic_sessios_table}.doctor_id
                    LEFT JOIN {$clinic_table} ON {$clinic_table}.id = {$clinic_sessios_table}.clinic_id ";
            
            // Base conditions
            $where_conditions = " WHERE {$user_table}.user_status=0 ";
            
            // Ensure we're filtering for a specific doctor
            if (!empty($request_data['doctor_id'])) {
                $doctor_id = (int)$request_data['doctor_id'];
                $where_conditions .= " AND {$clinic_sessios_table}.doctor_id={$doctor_id}";
            } else if ($this->getLoginUserRole() === $this->getDoctorRole()) {
                // If no doctor_id provided but user is a doctor, use current user's ID
                $where_conditions .= " AND {$clinic_sessios_table}.doctor_id=" . get_current_user_id();
            }
            
            // Add clinic filter if provided
            if (!empty($request_data['clinic_id'])) {
                $clinic_id = (int)$request_data['clinic_id'];
                $where_conditions .= " AND {$clinic_sessios_table}.clinic_id={$clinic_id}";
            } else {
                // Default clinic if no specific clinic requested
                $where_conditions .= " AND {$clinic_sessios_table}.clinic_id=" . kcGetDefaultClinicId();
            }
            
            // Add service_id filter if provided - CRITICAL FIX
            if (!empty($request_data['service_id'])) {
                $service_id = (int)$request_data['service_id'];
                $where_conditions .= " AND {$clinic_sessios_table}.service_id={$service_id}";
                
                error_log("Filtering sessions for service_id: {$service_id}");
                
                $service_query = $query . $where_conditions;
                error_log("Service query: {$service_query}");
                
                $clinic_sessions = $this->db->get_results($service_query);
                
                if (!empty($clinic_sessions)) {
                    error_log("Found " . count($clinic_sessions) . " sessions with service_id: {$service_id}");
                    $clinic_sessions = kcClinicSession($clinic_sessions);
                    
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Clinic session list', 'kc-lang'),
                        'data' => [
                            'clinic_sessions' => $clinic_sessions
                        ]
                    ]);
                } else {
                    error_log("No sessions found with service_id: {$service_id}");
                    
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('No clinic session list found', 'kc-lang'),
                        'data' => [
                            'clinic_sessions' => []
                        ]
                    ]);
                }
            } else {
                // If no service_id specified, get regular doctor sessions (NULL service_id)
                $where_conditions .= " AND ({$clinic_sessios_table}.service_id IS NULL OR {$clinic_sessios_table}.service_id = 0)";
                
                $clinic_sessions = $this->db->get_results($query . $where_conditions);
                
                if (empty($clinic_sessions)) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('No clinic session list found', 'kc-lang'),
                        'data' => [
                            'clinic_sessions' => []
                        ]
                    ]);
                }
                
                $clinic_sessions = kcClinicSession($clinic_sessions);
                
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Clinic session list', 'kc-lang'),
                    'data' => [
                        'clinic_sessions' => $clinic_sessions
                    ]
                ]);
            }
    }
    
    
    public function save() {
        if (!kcCheckPermission('doctor_session_add')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }
    
        // Add detailed debugging to track incoming request data
        error_log("KCClinicSessionController::save - Raw POST data: " . print_r($_POST, true));
        
        $request_data = $this->request->getInputs();
        error_log("KCClinicSessionController::save - Processed request data: " . print_r($request_data, true));
    
        // Insert clinic session...
        switch ($this->getLoginUserRole()) {
            case $this->getReceptionistRole():
                $clinic_id = kcGetClinicIdOfReceptionist();
                break;
            case $this->getClinicAdminRole():
                $clinic_id = kcGetClinicIdOfClinicAdmin();
                break;
            default:
                // Handle clinic_id properly whether it's an object or string
                $clinic_id = isset($request_data['clinic_id']) ? 
                    (is_array($request_data['clinic_id']) || is_object($request_data['clinic_id']) ? 
                        $request_data['clinic_id']['id'] : $request_data['clinic_id']) : 
                    kcGetDefaultClinicId();
                break;
        }
            
        $session = $request_data;
    
        $clinic_session = new KCClinicSession();
    
        if (isset($request_data['id']) && $request_data['id'] !== '') {
            if (!((new KCClinicSession())->sessionPermissionUserWise($request_data['id']))) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
    
            $request_data['id'] = (int)$request_data['id'];
            // delete parent session
            $clinic_session->delete(['id' => $request_data['id']]);
    
            // delete child session
            $clinic_session->delete(['parent_id' => $request_data['id']]);
        }
    
        $parent_id = 0;
    
        // Get doctor_id - handle both object format and direct ID
        $doctor_id = $this->getLoginUserRole() === $this->getDoctorRole() ? 
                    get_current_user_id() : 
                    (isset($session['doctor_id']) ? 
                        (is_array($session['doctor_id']) || is_object($session['doctor_id']) ? 
                            (int)$session['doctor_id']['id'] : (int)$session['doctor_id']) : 
                        (isset($session['doctors']['id']) ? (int)$session['doctors']['id'] : 0));
    
        // Get service ID - CRUCIAL FIX
        $service_id = null;
        if (isset($session['service_id']) && !empty($session['service_id'])) {
            // Convert to integer or handle string/object cases
            if (is_array($session['service_id']) || is_object($session['service_id'])) {
                // Handle case where service_id is an object with 'id' property
                $service_id = isset($session['service_id']['id']) ? (int)$session['service_id']['id'] : null;
            } else {
                // Handle direct integer or string value
                $service_id = (int)$session['service_id'];
            }
            
            // Validate it's a positive integer
            if ($service_id <= 0) {
                $service_id = null;
            }
            
            error_log("Service-specific session for service_id: {$service_id}, doctor_id: {$doctor_id}");
        } else {
            error_log("Regular doctor session for doctor_id: {$doctor_id}");
        }
    
        // Ensure time_slot (buffertime) is properly set
        $buffer_time = isset($session['time_slot']) ? (int)$session['time_slot'] : 
                      (isset($session['buffertime']) ? (int)$session['buffertime'] : 0);
        
        // Delete existing sessions for this doctor, clinic, and service for the days being updated
        foreach ($session['days'] as $day) {
            if ($service_id) {
                // For service-specific sessions, delete only sessions for that specific service
                $this->db->query(
                    $this->db->prepare(
                        "DELETE FROM {$this->db->prefix}kc_clinic_sessions 
                         WHERE doctor_id = %d 
                         AND clinic_id = %d 
                         AND day = %s 
                         AND service_id = %d",
                        $doctor_id,
                        (int)$clinic_id,
                        $day['name'],
                        $service_id
                    )
                );
                error_log("Deleted service-specific sessions: day={$day['name']}, service_id={$service_id}");
            } else {
                // For regular doctor sessions, delete only sessions without a service_id
                $this->db->query(
                    $this->db->prepare(
                        "DELETE FROM {$this->db->prefix}kc_clinic_sessions 
                         WHERE doctor_id = %d 
                         AND clinic_id = %d 
                         AND day = %s 
                         AND (service_id IS NULL OR service_id = 0)",
                        $doctor_id,
                        (int)$clinic_id,
                        $day['name']
                    )
                );
                error_log("Deleted regular sessions: day={$day['name']}");
            }
        }
    
        $result = false;
    
        foreach ($session['days'] as $day) {
            // Only process if the day has slots
            if (isset($day['slots']) && !empty($day['slots'])) {
                foreach ($day['slots'] as $key => $slot) {
                    $start_time = date('H:i:s', strtotime($slot['start']));
                    $end_time = date('H:i:s', strtotime($slot['end']));
                    $session_temp = [
                        'clinic_id' => (int)$clinic_id,
                        'doctor_id' => $doctor_id,
                        'day' => $day['name'],
                        'start_time' => $start_time,
                        'end_time' => $end_time,
                        'buffertime' => $buffer_time,
                        'created_at' => current_time('Y-m-d H:i:s'),
                        'parent_id' => (int)$parent_id === 0 ? null : (int)$parent_id
                    ];
    
                    // Add service ID if provided - CRUCIAL FIX
                    if ($service_id) {
                        $session_temp['service_id'] = $service_id;
                        error_log("Adding service_id {$service_id} to session row");
                    } else {
                        // Explicitly set NULL for regular sessions
                        $session_temp['service_id'] = null;
                        error_log("Setting NULL service_id for regular doctor session");
                    }
    
                    if ($parent_id === 0) {
                        $parent_id = (int)$clinic_session->insert($session_temp);
                        $result = $parent_id > 0;
                        error_log("Created parent session with ID: {$parent_id}, service_id: " . 
                                  ($service_id ? $service_id : 'NULL'));
                    } else {
                        $child_id = (int)$clinic_session->insert($session_temp);
                        $result = $child_id > 0;
                        error_log("Created child session with ID: {$child_id}, parent_id: {$parent_id}");
                    }
                }
            }
        }
        
        if (!$result) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Failed to save clinic session. Please try again.', 'kc-lang')
            ]);
        }
    
        $activity_message = $service_id ? 
            esc_html__('Service-specific doctor session has been saved successfully', 'kc-lang') : 
            esc_html__('Doctor session has been saved successfully', 'kc-lang');
    
        $activity_data = [
            'clinic_id' => $clinic_id,
            'doctor_id' => $doctor_id
        ];
        
        // Add service ID to activity data if provided
        if ($service_id) {
            $activity_data['service_id'] = $service_id;
        }
    
        kcLogActivity(
            'create_doctor_session',
            sprintf($activity_message, $clinic_id),
            $activity_data
        );
    
        wp_send_json([
            'status' => true,
            'message' => $service_id ? 
                esc_html__('Service-specific doctor session saved successfully', 'kc-lang') : 
                esc_html__('Doctor session saved successfully', 'kc-lang')
        ]);
    }
    
    public function delete () {
        // Unchanged - keeping original code
        if ( ! kcCheckPermission( 'doctor_session_delete' ) ) {
	        wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        $clinic_session = new KCClinicSession();

        if (isset($request_data['session_id']) && $request_data['session_id'] !== '' ) {

            if(!((new KCClinicSession())->sessionPermissionUserWise($request_data['session_id']))){
	            wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data['session_id'] = (int)$request_data['session_id'];
            // delete parent session
            $clinic_session->delete(['id' => $request_data['session_id']]);

            // delete child session
            $clinic_session->delete(['parent_id' => $request_data['session_id']]);

	        wp_send_json([
                'status' => true,
                'message' => esc_html__('Doctor session deleted successfully', 'kc-lang')
            ]);
        }
    }

    public function saveTimeZoneOption(){
        // Unchanged - keeping original code
		if(  $this->getLoginUserRole() !== 'administrator'){
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}
        $request_data = $this->request->getInputs();
        $status = false;
        if(isset($request_data['time_status']) && !empty($request_data['time_status']) ){
            update_option(KIVI_CARE_PREFIX.'timezone_understand',$request_data['time_status']);
            $status = true;
        }
        $response = [
            'status' => true,
            'data' => $status,
        ];
	    wp_send_json($response);
    }
}