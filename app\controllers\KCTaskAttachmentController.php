<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCTask;
use App\models\KCTaskAssignee;
use App\models\KCTaskAttachment;
use Exception;

class KCTaskAttachmentController extends KCBase {

	public $db;

	/**
	 * @var KCRequest
	 */
	private $request;

	public function __construct() {
		global $wpdb;
		$this->db = $wpdb;
		$this->request = new KCRequest();
		parent::__construct();
	}

	public function index() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		
		// Verify task exists and user has access to it
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}
		
		// Get the attachments for this task
		$attachments = (new KCTaskAttachment())->getTaskAttachments($task_id);

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Task attachments', 'kc-lang'),
			'data' => $attachments
		]);
	}

	public function upload() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();
		error_log('TaskAttachmentController upload request: ' . json_encode($request_data));
		error_log('FILES array: ' . json_encode($_FILES));

		if (!isset($request_data['task_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		
		// Verify task exists
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}

		// Handle file upload
		if (!isset($_FILES['file'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('No file provided', 'kc-lang')
			]);
		}

		try {
			// Process file upload using WordPress media functions
			$attachment_id = media_handle_upload('file', 0);

			if (is_wp_error($attachment_id)) {
				wp_send_json([
					'status' => false,
					'message' => $attachment_id->get_error_message()
				]);
			}

			// Get file details
			$file_url = wp_get_attachment_url($attachment_id);
			$file_name = basename(get_attached_file($attachment_id));
			
			// Save attachment record
			$attachment_model = new KCTaskAttachment();
			$result = $attachment_model->addAttachment(
				$task_id, 
				$attachment_id, 
				$file_name, 
				$file_url
			);

			if ($result) {
				// Get all attachments for the task to return updated list
				$attachments = $attachment_model->getTaskAttachments($task_id);
				
				// Send notification about the attachment upload
				if (function_exists('sendTaskNotificationEmails')) {
					$user_id = get_current_user_id();
					
					// Get assignees for notifications
					$task_assignee_model = new KCTaskAssignee();
					$assignees = $task_assignee_model->getTaskAssignees($task_id);
					$assignee_ids = array_map(function($a) {
						return $a->assignee_id;
					}, $assignees);
					
					// Also notify task creator if not in assignees
					if (!in_array($task->creator_id, $assignee_ids)) {
						$assignee_ids[] = $task->creator_id;
					}
					
					// Remove the current user from notifications
					$assignee_ids = array_diff($assignee_ids, [$user_id]);
					
					// Only send notification if there are recipients
					if (!empty($assignee_ids)) {
						// Get user info
						$user_info = get_userdata($user_id);
						$user_name = $user_info ? $user_info->display_name : esc_html__('A user', 'kc-lang');
						
						// Additional data for notification
						$additional_data = [
							'file_name' => $file_name,
							'uploaded_by' => $user_name,
							'uploaded_by_id' => $user_id
						];
						
						// Send notifications
						sendTaskNotificationEmails($task_id, $assignee_ids, 'attachment_added', '', $additional_data);
					}
				}
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('File uploaded successfully', 'kc-lang'),
					'data' => $attachments
				]);
			} else {
				// If attachment record failed to save, delete the uploaded file
				wp_delete_attachment($attachment_id, true);
				
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to save attachment record', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function delete() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Attachment ID is required', 'kc-lang')
			]);
		}

		$attachment_id = (int)$request_data['id'];
		$user_id = get_current_user_id();
		$user_role = $this->getLoginUserRole();

		try {
			$attachment_model = new KCTaskAttachment();
			$attachment = $attachment_model->get_by(['id' => $attachment_id], '=', true);
			
			if (empty($attachment)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Attachment not found', 'kc-lang')
				]);
			}
			
			// Verify the user has permission to delete this attachment
			// Only the uploader, admin, or clinic admin can delete attachments
			if ($attachment->uploaded_by != $user_id && 
				$user_role !== $this->getAdminRole() && 
				$user_role !== $this->getClinicAdminRole()) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('You do not have permission to delete this attachment', 'kc-lang')
				]);
			}
			
			// Store file details for notification before deleting
			$task_id = $attachment->task_id;
			$file_name = $attachment->file_name;
			
			$result = $attachment_model->deleteAttachment($attachment_id);

			if ($result) {
				// Get all attachments for the task to return updated list
				$attachments = $attachment_model->getTaskAttachments($task_id);
				
				// Send notification about the attachment deletion
				if (function_exists('sendTaskNotificationEmails')) {
					// Get task for notifications
					$task = (new KCTask())->getTaskById($task_id);
					
					if ($task) {
						// Get task assignees for notifications
						$task_assignee_model = new KCTaskAssignee();
						$assignees = $task_assignee_model->getTaskAssignees($task_id);
						$assignee_ids = array_map(function($a) {
							return $a->assignee_id;
						}, $assignees);
						
						// Also notify task creator if not in assignees
						if (!in_array($task->creator_id, $assignee_ids)) {
							$assignee_ids[] = $task->creator_id;
						}
						
						// Remove the current user from notifications
						$assignee_ids = array_diff($assignee_ids, [$user_id]);
						
						// Only send notification if there are recipients
						if (!empty($assignee_ids)) {
							// Get user info
							$user_info = get_userdata($user_id);
							$user_name = $user_info ? $user_info->display_name : esc_html__('A user', 'kc-lang');
							
							// Additional data for notification
							$additional_data = [
								'file_name' => $file_name,
								'deleted_by' => $user_name,
								'deleted_by_id' => $user_id
							];
							
							// Send notifications
							sendTaskNotificationEmails($task_id, $assignee_ids, 'attachment_deleted', '', $additional_data);
						}
					}
				}
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Attachment deleted successfully', 'kc-lang'),
					'data' => $attachments
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to delete attachment', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function download() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Attachment ID is required', 'kc-lang')
			]);
		}

		$attachment_id = (int)$request_data['id'];

		try {
			$attachment_model = new KCTaskAttachment();
			$attachment = $attachment_model->get_by(['id' => $attachment_id], '=', true);
			
			if (empty($attachment)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Attachment not found', 'kc-lang')
				]);
			}
			
			// Get the file path from WordPress attachment
			$file_path = get_attached_file($attachment->attachment_id);
			
			if (!file_exists($file_path)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('File not found on server', 'kc-lang')
				]);
			}
			
			// Return the file URL for the frontend to handle the download
			wp_send_json([
				'status' => true,
				'message' => esc_html__('Attachment found', 'kc-lang'),
				'data' => [
					'file_url' => $attachment->file_url,
					'file_name' => $attachment->file_name
				]
			]);
			
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}
}