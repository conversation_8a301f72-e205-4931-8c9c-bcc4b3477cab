<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTaskAttachment extends KCModel
{
    public function __construct()
    {
        parent::__construct('task_attachments');
    }

    /**
     * Add an attachment to a task
     *
     * @param int $task_id - Task ID
     * @param int $attachment_id - WordPress attachment ID
     * @param string $file_name - Original filename
     * @param string $file_url - WordPress attachment URL
     * @param int $user_id - User ID (default: current user)
     * @return int|bool - Attachment ID or false
     */
    public function addAttachment($task_id, $attachment_id, $file_name, $file_url, $user_id = null)
    {
        if ($user_id === null) {
            $user_id = get_current_user_id();
        }

        return $this->insert([
            'task_id' => (int)$task_id,
            'file_name' => sanitize_text_field($file_name),
            'file_url' => esc_url_raw($file_url),
            'attachment_id' => (int)$attachment_id,
            'uploaded_by' => (int)$user_id,
            'uploaded_at' => current_time('mysql')
        ]);
    }

    /**
     * Delete an attachment
     *
     * @param int $attachment_id - Attachment ID
     * @return bool - Success
     */
    public function deleteAttachment($attachment_id)
    {
        $attachment = $this->get_by(['id' => (int)$attachment_id], '=', true);
        
        if ($attachment) {
            // Delete the WordPress attachment
            wp_delete_attachment($attachment->attachment_id, true);
            
            // Delete the attachment record
            return $this->delete(['id' => (int)$attachment_id]);
        }
        
        return false;
    }

    /**
     * Get all attachments for a task
     *
     * @param int $task_id - Task ID
     * @return array - Attachments data
     */
    public function getTaskAttachments($task_id)
    {
        global $wpdb;

        $table_name = $this->get_table_name();
        $users_table = $wpdb->base_prefix . 'users';

        $query = "
            SELECT $table_name.*, $users_table.display_name AS uploader_name
            FROM $table_name
            JOIN $users_table ON $table_name.uploaded_by = $users_table.ID
            WHERE $table_name.task_id = %d
        ";

        $prepared_query = $wpdb->prepare($query, (int)$task_id);
        return $wpdb->get_results($prepared_query);
    }
}