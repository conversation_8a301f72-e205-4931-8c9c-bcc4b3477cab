<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTaskComment extends KCModel
{
    public function __construct()
    {
        parent::__construct('task_comments');
    }

    /**
     * Add a comment to a task
     *
     * @param int $task_id - Task ID
     * @param string $comment - Comment text
     * @param int $user_id - User ID (default: current user)
     * @return int|bool - Comment ID or false
     */
    public function addComment($task_id, $comment, $user_id = null)
    {
        if ($user_id === null) {
            $user_id = get_current_user_id();
        }

        return $this->insert([
            'task_id' => (int)$task_id,
            'user_id' => (int)$user_id,
            'comment' => sanitize_textarea_field($comment),
            'created_at' => current_time('mysql')
        ]);
    }

    /**
     * Delete a comment
     *
     * @param int $comment_id - Comment ID
     * @return bool - Success
     */
    public function deleteComment($comment_id)
    {
        return $this->delete(['id' => (int)$comment_id]);
    }

    /**
     * Get all comments for a task
     *
     * @param int $task_id - Task ID
     * @return array - Comments data
     */
    public function getTaskComments($task_id)
    {
        global $wpdb;

        $table_name = $this->get_table_name();
        $users_table = $wpdb->base_prefix . 'users';

        $query = "
            SELECT $table_name.*, $users_table.display_name AS user_name
            FROM $table_name
            JOIN $users_table ON $table_name.user_id = $users_table.ID
            WHERE $table_name.task_id = %d
            ORDER BY $table_name.created_at DESC
        ";

        $prepared_query = $wpdb->prepare($query, (int)$task_id);
        return $wpdb->get_results($prepared_query);
    }
}