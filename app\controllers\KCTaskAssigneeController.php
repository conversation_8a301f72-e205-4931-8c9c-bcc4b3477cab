<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCTask;
use App\models\KCTaskAssignee;
use Exception;

class KCTaskAssigneeController extends KCBase {

	public $db;

	/**
	 * @var KCRequest
	 */
	private $request;

	public function __construct() {
		global $wpdb;
		$this->db = $wpdb;
		$this->request = new KCRequest();
		parent::__construct();
	}

	public function index() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		
		// Verify task exists
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}
		
		// Get the assignees for this task
		$assignees = (new KCTaskAssignee())->getTaskAssignees($task_id);

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Task assignees', 'kc-lang'),
			'data' => $assignees
		]);
	}

	public function getAssignedTasks() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();
		$user_id = get_current_user_id();
		
		// Override user_id if provided and user has admin permissions
		if (!empty($request_data['user_id']) && 
			($this->getLoginUserRole() === $this->getAdminRole() || 
			 $this->getLoginUserRole() === $this->getClinicAdminRole())) {
			$user_id = (int)$request_data['user_id'];
		}
		
		// Apply filters
		$filters = [];
		
		if (!empty($request_data['status']) && $request_data['status'] !== 'all') {
			$filters['status'] = sanitize_text_field($request_data['status']);
		}
		
		if (!empty($request_data['priority']) && $request_data['priority'] !== 'all') {
			$filters['priority'] = sanitize_text_field($request_data['priority']);
		}
		
		if (isset($request_data['is_archived'])) {
			$filters['is_archived'] = (int)$request_data['is_archived'];
		}
		
		if (!empty($request_data['clinic_id'])) {
			$filters['clinic_id'] = (int)$request_data['clinic_id'];
		}

		// Handle sorting
		if (!empty($request_data['orderby'])) {
			$filters['orderby'] = sanitize_text_field($request_data['orderby']);
			$filters['order'] = !empty($request_data['order']) ? sanitize_text_field($request_data['order']) : 'ASC';
		}
		
		// Get assigned tasks
		$tasks = (new KCTaskAssignee())->getAssignedTasks($user_id, $filters);

		if (empty($tasks)) {
			wp_send_json([
				'status' => true,
				'message' => esc_html__('No assigned tasks found', 'kc-lang'),
				'data' => [],
				'total' => 0
			]);
		}

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Assigned tasks retrieved successfully', 'kc-lang'),
			'data' => $tasks,
			'total' => count($tasks)
		]);
	}

	public function assignTask() {
		if (!kcCheckPermission('task_edit')) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id']) || !isset($request_data['assignee_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID and assignee ID are required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		$assignee_id = (int)$request_data['assignee_id'];
		
		// Verify task exists
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}
		
		// Verify assignee exists and is a valid KiviCare user
		$assignee = get_userdata($assignee_id);
		if (!$assignee || !$this->isValidKivicareUser($assignee_id)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Invalid assignee', 'kc-lang')
			]);
		}

		try {
			$task_assignee_model = new KCTaskAssignee();
			$result = $task_assignee_model->assignTask($task_id, $assignee_id);

			if ($result) {
				// Get all assignees for the task to return updated list
				$assignees = $task_assignee_model->getTaskAssignees($task_id);
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('User assigned to task successfully', 'kc-lang'),
					'data' => $assignees
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to assign user to task', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function unassignTask() {
		if (!kcCheckPermission('task_edit')) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id']) || !isset($request_data['assignee_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID and assignee ID are required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		$assignee_id = (int)$request_data['assignee_id'];

		try {
			$task_assignee_model = new KCTaskAssignee();
			$result = $task_assignee_model->unassignTask($task_id, $assignee_id);

			if ($result) {
				// Get all assignees for the task to return updated list
				$assignees = $task_assignee_model->getTaskAssignees($task_id);
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('User unassigned from task successfully', 'kc-lang'),
					'data' => $assignees
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to unassign user from task', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function completeTask() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		$user_id = get_current_user_id();

		try {
			$task_assignee_model = new KCTaskAssignee();
			
			// Verify this user is actually assigned to this task
			$assignees = $task_assignee_model->getTaskAssignees($task_id);
			$is_assigned = false;
			
			foreach ($assignees as $assignee) {
				if ($assignee->assignee_id == $user_id) {
					$is_assigned = true;
					break;
				}
			}
			
			if (!$is_assigned) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('You are not assigned to this task', 'kc-lang')
				]);
			}
			
			$result = $task_assignee_model->completeTask($task_id, $user_id);

			if ($result) {
				// Check if all assignees have completed the task
				$updated_assignees = $task_assignee_model->getTaskAssignees($task_id);
				$all_completed = true;
				
				foreach ($updated_assignees as $assignee) {
					if (empty($assignee->completed_at)) {
						$all_completed = false;
						break;
					}
				}
				
				// If all assignees have completed the task, update the task status to completed
				if ($all_completed) {
					(new KCTask())->updateTaskStatus($task_id, 'completed');
				}
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Task marked as completed', 'kc-lang'),
					'data' => $updated_assignees,
					'all_completed' => $all_completed
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to mark task as completed', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}
	
	/**
	 * Check if a user is a valid KiviCare user (admin, doctor, receptionist, patient)
	 *
	 * @param int $user_id - User ID to check
	 * @return bool - Whether the user is a valid KiviCare user
	 */
	private function isValidKivicareUser($user_id) {
		$valid_roles = [
			$this->getAdminRole(),
			$this->getClinicAdminRole(),
			$this->getDoctorRole(),
			$this->getReceptionistRole(),
			$this->getPatientRole()
		];
		
		$user = get_userdata($user_id);
		
		if (!$user) {
			return false;
		}
		
		foreach ($valid_roles as $role) {
			if (in_array($role, $user->roles)) {
				return true;
			}
		}
		
		return false;
	}
}